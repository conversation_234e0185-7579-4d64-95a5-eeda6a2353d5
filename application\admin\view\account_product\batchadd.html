<form id="batchadd-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">批量数据:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-batch_data" class="form-control" name="batch_data" rows="15" placeholder="请输入商品数据，每行一个，格式：链接 价格 口令&#10;例如：&#10;https://example.com/product1 9.99 ABC123&#10;https://example.com/product2 19.99 DEF456&#10;https://example.com/product3 29.99"></textarea>
            <span class="help-block">
                <strong>格式说明：</strong><br>
                • 每行一个商品，格式：链接 价格 口令（口令可选）<br>
                • 链接、价格、口令之间用空格分隔<br>
                • 价格必须是数字，支持小数<br>
                • 口令可以为空<br>
                • 相同价格的商品不能重复添加
            </span>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
// 等待页面加载完成
window.onload = function() {
    // 等待jQuery加载
    function waitForJQuery() {
        if (typeof $ !== 'undefined') {
            // 重写表单提交处理
            $("#batchadd-form").off('submit').on('submit', function(e) {
                e.preventDefault();
                console.log('表单提交中...');
                
                var formData = $(this).serialize();
                
                $.ajax({
                    url: $(this).attr('action') || window.location.href,
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(ret) {
                        console.log('AJAX成功返回:', ret);
                        if (ret.code == 1) {
                            // 显示成功消息
                            if (parent.Toastr) {
                                parent.Toastr.success(ret.msg || '操作成功');
                            }
                            
                            // 关闭当前弹窗
                            try {
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                                console.log('窗口已关闭');
                            } catch(e) {
                                console.log('关闭窗口失败:', e);
                            }
                            
                            // 刷新父窗口的表格
                            try {
                                parent.$("#table").bootstrapTable('refresh');
                                console.log('表格已刷新');
                            } catch(e) {
                                console.log('刷新表格失败，尝试上级窗口');
                                try {
                                    parent.parent.$("#table").bootstrapTable('refresh');
                                    console.log('上级窗口表格已刷新');
                                } catch(e2) {
                                    console.log('无法刷新表格:', e2);
                                }
                            }
                        } else {
                            // 显示错误消息
                            if (parent.Toastr) {
                                parent.Toastr.error(ret.msg || '操作失败');
                            } else {
                                alert(ret.msg || '操作失败');
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('AJAX错误:', error);
                        if (parent.Toastr) {
                            parent.Toastr.error('请求失败');
                        } else {
                            alert('请求失败');
                        }
                    }
                });
                
                return false;
            });
            
        } else {
            // 如果jQuery还没加载，100ms后再试
            setTimeout(waitForJQuery, 100);
        }
    }
    waitForJQuery();
};
</script>


