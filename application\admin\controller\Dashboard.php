<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;

/**
 * 控制台
 */
class Dashboard extends Backend
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['getChannelData','getChannelDetail'];
    
    /**
     * 查看
     */
    public function index()
    {
        // 获取今日收款总额
        $today_amount = $this->getTodayAmount();

        // 获取今日总成功率
        $recharge_ratio = $this->getRechargeRatio();

        // 获取账号统计
        $account_stats = $this->getAccountStats();

        // 获取各通道数据
        $channel_stats = $this->getChannelStats();

        // 获取近7天收款统计
        $recharge_list = $this->getRechargeList();

        $this->view->assign([
            'today_amount' => number_format($today_amount, 2),
            'recharge_ratio' => $recharge_ratio . '%',
            'normal_count' => $account_stats['normal'],
            'abnormal_count' => $account_stats['abnormal'],
            'recharge_list' => $recharge_list
        ]);
        
        return $this->view->fetch();
    }

    /**
     * 获取通道详情数据 - AJAX接口
     */
    public function getChannelData()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);
        $search = $this->request->param('search', '');
        $sort = $this->request->param('sort', '');
        $order = $this->request->param('order', 'asc');

        // 获取所有通道数据
        $channels = $this->getAllChannelStats();
        
        // 搜索过滤
        if (!empty($search)) {
            $channels = array_filter($channels, function($channel) use ($search) {
                return stripos($channel['name'], $search) !== false || 
                       stripos($channel['code'], $search) !== false;
            });
        }
        

        
        // 排序
        if (!empty($sort)) {
            usort($channels, function($a, $b) use ($sort, $order) {
                $aVal = $a[$sort] ?? 0;
                $bVal = $b[$sort] ?? 0;
                
                if (is_string($aVal)) {
                    $result = strcasecmp($aVal, $bVal);
                } else {
                    $result = $aVal <=> $bVal;
                }
                
                return $order === 'desc' ? -$result : $result;
            });
        }
        
        // 分页
        $total = count($channels);
        $offset = ($page - 1) * $limit;
        $channels = array_slice($channels, $offset, $limit);
        
        $this->success('获取成功', '', [
            'data' => $channels,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }



    /**
     * 刷新通道数据 - AJAX接口
     */
    public function refreshChannel()
    {
        $channel_code = $this->request->param('channel_code');

        if (empty($channel_code)) {
            $this->error('通道代码不能为空');
        }

        // 这里可以添加具体的刷新逻辑
        // 比如重新检查账号状态、更新缓存等

        $this->success('刷新成功');
    }

    /**
     * 获取通道详情 - AJAX接口
     */
    public function getChannelDetail()
    {
        $channel_code = $this->request->param('channel_code');

        if (empty($channel_code)) {
            $this->error('通道代码不能为空');
        }

        $orderChannelField = $this->getChannelFieldName();
        $accountChannelField = $this->getAccountChannelFieldName();

        // 获取通道基本信息
        $channel = Db::name('channel')
            ->where('code', $channel_code)
            ->field('code,name,notes,status')
            ->find();

        if (!$channel) {
            $this->error('通道不存在');
        }

        // 获取今日数据
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        $amount = Db::name('order')
            ->where($orderChannelField, $channel_code)
            ->where('pay_status', 1)
            ->where('create_time', 'between', [$todayStart, $todayEnd])
            ->sum('amount') ?: 0;

        $total = Db::name('order')
            ->where($orderChannelField, $channel_code)
            ->where('create_time', 'between', [$todayStart, $todayEnd])
            ->count();

        $success = Db::name('order')
            ->where($orderChannelField, $channel_code)
            ->where('pay_status', 1)
            ->where('create_time', 'between', [$todayStart, $todayEnd])
            ->count();

        $ratio = $total > 0 ? round(($success / $total) * 100, 1) : 0;

        // 检查账号表是否存在并获取账号统计
        try {
            $normal_count = Db::name('account')
                ->where($accountChannelField, $channel_code)
                ->where('status', 'normal')
                ->count();

            $abnormal_count = Db::name('account')
                ->where($accountChannelField, $channel_code)
                ->where('status', 'hidden')
                ->count();
        } catch (\Exception $e) {
            $normal_count = 0;
            $abnormal_count = 0;
        }

        // 获取7天趋势数据
        $trend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dayStart = strtotime($date . ' 00:00:00');
            $dayEnd = strtotime($date . ' 23:59:59');
            $dayAmount = Db::name('order')
                ->where($orderChannelField, $channel_code)
                ->where('pay_status', 1)
                ->where('create_time', 'between', [$dayStart, $dayEnd])
                ->sum('amount') ?: 0;
            $trend[] = floatval($dayAmount);
        }

        $detail = [
            'name' => $channel['name'],
            'code' => $channel['code'],
            'notes' => $channel['notes'],
            'amount' => floatval($amount),
            'ratio' => floatval($ratio),
            'normal' => intval($normal_count),
            'abnormal' => intval($abnormal_count),
            'trend' => $trend
        ];

        $this->success('获取成功', '', $detail);
    }

    /**
     * 获取订单表中通道字段名
     */
    private function getChannelFieldName()
    {
        // 检查订单表结构，确定使用哪个字段名
        try {
            $fields = Db::query("SHOW COLUMNS FROM " . config('database.prefix') . "order");
            $fieldNames = array_column($fields, 'Field');
            
            if (in_array('channel_code', $fieldNames)) {
                return 'channel_code';
            } elseif (in_array('channel', $fieldNames)) {
                return 'channel';
            } else {
                // 默认使用 channel_code
                return 'channel_code';
            }
        } catch (\Exception $e) {
            // 如果查询失败，默认使用 channel_code
            return 'channel_code';
        }
    }

    /**
     * 获取账号表中通道字段名
     */
    private function getAccountChannelFieldName()
    {
        try {
            $fields = Db::query("SHOW COLUMNS FROM " . config('database.prefix') . "account");
            $fieldNames = array_column($fields, 'Field');
            
            if (in_array('channel_code', $fieldNames)) {
                return 'channel_code';
            } elseif (in_array('channel', $fieldNames)) {
                return 'channel';
            } else {
                return 'channel';
            }
        } catch (\Exception $e) {
            return 'channel';
        }
    }

    /**
     * 获取今日收款总额
     */
    private function getTodayAmount()
    {
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        return Db::name('order')
            ->where('pay_status', 1)
            ->where('create_time', 'between', [$todayStart, $todayEnd])
            ->sum('amount') ?: 0;
    }

    /**
     * 获取今日总成功率
     */
    private function getRechargeRatio()
    {
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        $total = Db::name('order')
            ->where('create_time', 'between', [$todayStart, $todayEnd])
            ->count();
        $success = Db::name('order')
            ->where('pay_status', 1)
            ->where('create_time', 'between', [$todayStart, $todayEnd])
            ->count();

        return $total > 0 ? round(($success / $total) * 100, 1) : 0;
    }

    /**
     * 获取账号统计
     */
    private function getAccountStats()
    {
        // 检查账号表是否存在
        try {
            $normal = Db::name('account')->where('status', 'normal')->count();
            $abnormal = Db::name('account')->where('status', 'hidden')->count();
        } catch (\Exception $e) {
            // 如果账号表不存在，返回0
            $normal = 0;
            $abnormal = 0;
        }

        return [
            'normal' => $normal,
            'abnormal' => $abnormal
        ];
    }

    /**
     * 获取通道统计
     */
    private function getChannelStats()
    {
        $orderChannelField = $this->getChannelFieldName();
        $accountChannelField = $this->getAccountChannelFieldName();

        // 从数据库获取前两个启用的通道作为主要通道
        $channels = Db::name('channel')
            ->where('status', 1)
            ->field('code,name')
            ->limit(2)
            ->select();

        $stats = [];

        foreach ($channels as $channelInfo) {
            $channel = $channelInfo['code'];
            $todayStart = strtotime(date('Y-m-d 00:00:00'));
            $todayEnd = strtotime(date('Y-m-d 23:59:59'));

            $amount = Db::name('order')
                ->where($orderChannelField, $channel)
                ->where('pay_status', 1)
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->sum('amount') ?: 0;

            $total = Db::name('order')
                ->where($orderChannelField, $channel)
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->count();

            $success = Db::name('order')
                ->where($orderChannelField, $channel)
                ->where('pay_status', 1)
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->count();
                
            $ratio = $total > 0 ? round(($success / $total) * 100, 1) : 0;
            
            // 检查账号表是否存在并获取账号统计
            try {
                $abnormal_count = Db::name('account')
                    ->where($accountChannelField, $channel)
                    ->where('status', 'hidden')
                    ->count();
            } catch (\Exception $e) {
                $abnormal_count = 0;
            }
            
            $stats[$channel] = [
                'amount' => $amount,
                'ratio' => $ratio,
                'abnormal_count' => $abnormal_count
            ];
        }
        
        return $stats;
    }

    /**
     * 获取所有通道统计
     */
    private function getAllChannelStats()
    {
        $orderChannelField = $this->getChannelFieldName();
        $accountChannelField = $this->getAccountChannelFieldName();

        // 从数据库获取所有启用的通道
        $channels = Db::name('channel')
            ->where('status', 1)
            ->field('code,name')
            ->select();

        $stats = [];

        foreach ($channels as $channel) {
            $code = $channel['code'];
            $name = $channel['name'];
            $todayStart = strtotime(date('Y-m-d 00:00:00'));
            $todayEnd = strtotime(date('Y-m-d 23:59:59'));

            $amount = Db::name('order')
                ->where($orderChannelField, $code)
                ->where('pay_status', 1)
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->sum('amount') ?: 0;

            $total = Db::name('order')
                ->where($orderChannelField, $code)
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->count();

            $success = Db::name('order')
                ->where($orderChannelField, $code)
                ->where('pay_status', 1)
                ->where('create_time', 'between', [$todayStart, $todayEnd])
                ->count();
                
            $ratio = $total > 0 ? round(($success / $total) * 100, 1) : 0;
            
            // 检查账号表是否存在并获取账号统计
            try {
                $normal_count = Db::name('account')
                    ->where($accountChannelField, $code)
                    ->where('status', 'normal')
                    ->count();

                $abnormal_count = Db::name('account')
                    ->where($accountChannelField, $code)
                    ->where('status', 'hidden')
                    ->count();
            } catch (\Exception $e) {
                $normal_count = 0;
                $abnormal_count = 0;
            }

            $stats[] = [
                'name' => $name,
                'code' => $code,
                'amount' => floatval($amount),
                'ratio' => floatval($ratio),
                'normal' => intval($normal_count),
                'abnormal' => intval($abnormal_count)
            ];
        }
        
        return $stats;
    }

    /**
     * 获取近7天收款统计
     */
    private function getRechargeList()
    {
        $list = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dayStart = strtotime($date . ' 00:00:00');
            $dayEnd = strtotime($date . ' 23:59:59');
            $amount = Db::name('order')
                ->where('pay_status', 1)
                ->where('create_time', 'between', [$dayStart, $dayEnd])
                ->sum('amount') ?: 0;

            $list[] = [
                'date' => $date,
                'amount' => floatval($amount)
            ];
        }

        return $list;
    }




}