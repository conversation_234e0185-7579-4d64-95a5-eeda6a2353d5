<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mc_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-out_trade_no" data-rule="required" class="form-control" name="row[out_trade_no]" type="text" value="{$row.out_trade_no|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lc_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-local_trade_no" data-rule="required" class="form-control" name="row[local_trade_no]" type="text" value="{$row.local_trade_no|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('pay_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_status" data-rule="required" class="form-control" step="0.01" name="row[pay_status]" type="number" value="{$row.pay_status|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchants_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-merchants_id" data-rule="required" data-source="merchants/index" class="form-control selectpage" name="row[merchants_id]" type="text" value="{$row.merchants_id|htmlentities}" disabled>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_ip')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_ip" data-rule="" class="form-control" name="row[create_ip]" type="text" value="{$row.create_ip|htmlentities}" disabled>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel_code" data-rule="required" class="form-control" name="row[channel_code]" type="text" value="{$row.channel_code|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_url" data-rule="required" class="form-control" name="row[pay_url]" type="text" value="{$row.pay_url|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Callback_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-callback_url" data-rule="required" class="form-control" name="row[callback_url]" type="text" value="{$row.callback_url|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Callback_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-callback_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[callback_time]" type="text" value="{:$row.callback_time?datetime($row.callback_time):''}" disabled>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
