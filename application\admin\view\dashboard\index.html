<style type="text/css">
    /* 简洁控制台样式 */
    .dashboard-container {
        background: #f5f7fa;
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-header {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e8ed;
    }

    .dashboard-header h3 {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
        font-size: 24px;
    }

    /* 标签页样式 */
    .dashboard-tabs {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e8ed;
        margin-bottom: 20px;
        overflow: hidden;
    }

    .dashboard-tabs .nav-tabs {
        border-bottom: 1px solid #e1e8ed;
        margin: 0;
        background: #f8f9fa;
    }

    .dashboard-tabs .nav-tabs > li {
        margin-bottom: 0;
    }

    .dashboard-tabs .nav-tabs > li > a {
        border: none;
        border-radius: 0;
        padding: 15px 25px;
        color: #6c7b7f;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .dashboard-tabs .nav-tabs > li > a:hover {
        background: #e9ecef;
        color: #2c3e50;
    }

    .dashboard-tabs .nav-tabs > li.active > a,
    .dashboard-tabs .nav-tabs > li.active > a:hover,
    .dashboard-tabs .nav-tabs > li.active > a:focus {
        background: #fff;
        color: #3498db;
        border: none;
        border-bottom: 3px solid #3498db;
    }

    .dashboard-tabs .tab-content {
        padding: 20px;
    }

    /* 统计卡片样式 */
    .stat-card {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e8ed;
        transition: all 0.2s ease;
        border-left: 4px solid #3498db;
    }

    .stat-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .stat-card.success {
        border-left-color: #27ae60;
    }

    .stat-card.warning {
        border-left-color: #f39c12;
    }

    .stat-card.danger {
        border-left-color: #e74c3c;
    }

    .stat-card.info {
        border-left-color: #3498db;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 12px;
        color: white;
        font-weight: bold;
    }

    .stat-card.success .stat-icon {
        background: #27ae60;
    }

    .stat-card.warning .stat-icon {
        background: #f39c12;
    }

    .stat-card.danger .stat-icon {
        background: #e74c3c;
    }

    .stat-card.info .stat-icon {
        background: #3498db;
    }

    .stat-value {
        font-size: 28px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
        line-height: 1.2;
    }

    .stat-label {
        color: #6c7b7f;
        font-size: 14px;
        font-weight: 400;
    }

    /* 通道表格样式 */
    .channel-table-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e8ed;
        overflow: hidden;
    }

    .table-toolbar {
        padding: 15px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e1e8ed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .table-toolbar .search-box {
        flex: 1;
        max-width: 300px;
    }



    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
    }

    .status-badge.normal {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.abnormal {
        background: #f8d7da;
        color: #721c24;
    }

    .status-badge.warning {
        background: #fff3cd;
        color: #856404;
    }

    /* 图表容器样式 */
    .chart-container {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e8ed;
        padding: 20px;
        margin-bottom: 20px;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e1e8ed;
    }

    .chart-header h4 {
        margin: 0;
        color: #2c3e50;
        font-weight: 600;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px;
        }
        
        .stat-card {
            margin-bottom: 15px;
        }
        
        .stat-value {
            font-size: 24px;
        }
        
        .stat-icon {
            width: 45px;
            height: 45px;
            font-size: 20px;
        }

        .dashboard-tabs .nav-tabs > li > a {
            padding: 12px 15px;
            font-size: 14px;
        }

        .table-toolbar {
            flex-direction: column;
            align-items: stretch;
        }

        .table-toolbar .search-box {
            max-width: none;
        }
    }
</style>

<div class="dashboard-container">
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="dashboard-header">
            <h3>数据控制台</h3>
            <p style="margin: 8px 0 0 0; color: #6c7b7f;">实时监控系统运营数据</p>
        </div>

        <!-- 标签页导航 -->
        <div class="dashboard-tabs">
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#overview" aria-controls="overview" role="tab" data-toggle="tab">
                        <i class="fa fa-dashboard"></i> 总览
                    </a>
                </li>
                <li role="presentation">
                    <a href="#channels" aria-controls="channels" role="tab" data-toggle="tab">
                        <i class="fa fa-table"></i> 通道详情
                    </a>
                </li>

            </ul>

            <div class="tab-content">
                <!-- 总览标签页 -->
                <div role="tabpanel" class="tab-pane active" id="overview">
                    <!-- 总体数据统计 -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                            <div class="stat-card success">
                                <div class="stat-icon">$</div>
                                <div class="stat-value">{$today_amount}</div>
                                <div class="stat-label">今日收款总额</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                            <div class="stat-card info">
                                <div class="stat-icon">%</div>
                                <div class="stat-value">{$recharge_ratio}</div>
                                <div class="stat-label">今日总成功率</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                            <div class="stat-card success">
                                <div class="stat-icon">✓</div>
                                <div class="stat-value">{$normal_count}</div>
                                <div class="stat-label">正常账号总数</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                            <div class="stat-card danger">
                                <div class="stat-icon">!</div>
                                <div class="stat-value">{$abnormal_count}</div>
                                <div class="stat-label">异常账号总数</div>
                            </div>
                        </div>
                    </div>



                    <!-- 收款统计表格 -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="chart-container">
                                <div class="chart-header">
                                    <h4>近7天收款统计</h4>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>收款金额</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {volist name="recharge_list" id="vo"}
                                        <tr>
                                            <td>{$vo.date}</td>
                                            <td><strong>¥{$vo.amount}</strong></td>
                                        </tr>
                                        {/volist}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通道详情标签页 -->
                <div role="tabpanel" class="tab-pane" id="channels">
                    <div class="channel-table-container">
                        <div class="table-toolbar">
                            <div class="search-box">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="channelSearch" placeholder="搜索通道名称...">
                                    <span class="input-group-btn">
                                        <button class="btn btn-default" type="button">
                                            <i class="fa fa-search"></i>
                                        </button>
                                    </span>
                                </div>
                            </div>

                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover" id="channelTable">
                                <thead>
                                <tr>
                                    <th data-sort="name">通道名称 <i class="fa fa-sort"></i></th>
                                    <th data-sort="amount">今日收款 <i class="fa fa-sort"></i></th>
                                    <th data-sort="ratio">成功率 <i class="fa fa-sort"></i></th>
                                    <th data-sort="normal">正常账号 <i class="fa fa-sort"></i></th>
                                    <th data-sort="abnormal">异常账号 <i class="fa fa-sort"></i></th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody id="channelTableBody">
                                <!-- 通道数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>