<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use AlipayService;
use fast\Http;

/**
 * 首页接口
 * @ApiInternal
 */
class Isv extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 支付宝第三方应用授权回调接口
     * @ApiMethod (POST)
     */
    public function alipay_auth_callback()
    {
        try {
            $params = input();

            // 记录回调日志
            trace("支付宝第三方应用授权回调: " . json_encode($params), "info");

            // 验证必要参数（只需要 auth_code，state 用于判断账号）
            if (empty($params['app_auth_code'])) {
                echo '缺少授权码参数';
                exit;
            }

            // 处理授权回调逻辑
            $result = $this->processAuthCallback($params);

            if ($result) {
                echo '授权成功';
                exit;
            } else {
                echo '授权失败,请重试';
                exit;
            }

        } catch (Exception $e) {
            trace("支付宝授权回调异常: " . $e->getMessage(), "error");
            echo '系统异常,请重试';
            exit;
        }
    }
    
    /**
     * 处理授权回调逻辑
     */
    private function processAuthCallback($params)
    {
        try {
            // 使用 app_auth_code 换取 app_auth_token
            $app_auth_code = $params['app_auth_code'];
            $state = isset($params['state']) ? $params['state'] : '';

            // 验证 state 参数
            if (empty($state)) {
                trace("缺少state参数，无法确定账号", "error");
                return false;
            }

            // 调用获取 app_auth_token 的方法
            $result = $this->getAppAuthToken($app_auth_code);

            if ($result['success']) {
                // 保存授权信息到数据库，使用 state 参数判断账号（state 是 base64 编码的，需要解码）
                $decodedState = base64_decode($state);

                // 验证 base64 解码是否成功
                if ($decodedState === false || empty($decodedState)) {
                    trace("state参数base64解码失败，原始state: " . $state, "error");
                    return false;
                }

                $saveResult = $this->saveAuthInfo($result['data'], $decodedState);
                return $saveResult;
            } else {
                trace("获取app_auth_token失败: " . $result['message'], "error");
                return false;
            }
        } catch (Exception $e) {
            trace("处理授权回调异常: " . $e->getMessage(), "error");
            return false;
        }
    }

    /**
     * 使用 app_auth_code 换取 app_auth_token
     * 参考 Alidiandan.php 中 accountlog 方法的实现方式
     */
    public function getAppAuthToken($app_auth_code, $app_id = null)
    {
        try {
            if (empty($app_auth_code)) {
                return ['success' => false, 'message' => 'app_auth_code 不能为空'];
            }

            // 获取应用配置信息
            $appConfig = $this->getAppConfig($app_id);
            if (!$appConfig) {
                return ['success' => false, 'message' => '获取应用配置失败'];
            }

            $uri = 'https://openapi.alipay.com/gateway.do';

            // 构建请求参数，参考 accountlog 方法的实现
            $requestConfigs = array(
                'grant_type' => 'authorization_code',
                'code' => $app_auth_code
            );

            $commonConfigs = array(
                'app_id' => $appConfig['appid'],
                'method' => 'alipay.open.auth.token.app', // 第三方应用授权token接口
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'biz_content' => json_encode($requestConfigs),
            );

            // 使用 AlipayService 生成签名，参考 accountlog 方法
            $aliPay = new AlipayService();
            $aliPay->setAppid($appConfig['appid']);
            $aliPay->setRsaPrivateKey($appConfig['private_key']);
            $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);

            // 构建请求字符串
            $requestStr = $aliPay->buildOrderStr($commonConfigs);

            // 带重试机制的网络请求
            $maxRetries = 3;
            $retryDelay = 1000; // 毫秒
            $retryCount = 0;
            $lastError = null;
            $response = null;

            while ($retryCount < $maxRetries) {
                try {
                    $response = Http::post($uri, $requestStr);
                    
                    // 检查HTTP请求是否成功
                    if (!empty($response)) {
                        break; // 请求成功，跳出重试循环
                    }
                    
                    $lastError = '无响应数据';
                    trace("支付宝授权请求失败（尝试 " . ($retryCount + 1) . "/{$maxRetries}）: 无响应数据，app_auth_code: " . $app_auth_code, "error");
                    
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    trace("支付宝授权请求异常（尝试 " . ($retryCount + 1) . "/{$maxRetries}）: " . $e->getMessage() . ", app_auth_code: " . $app_auth_code, "error");
                }
                
                $retryCount++;
                if ($retryCount < $maxRetries) {
                    usleep($retryDelay * 1000); // 转换为微秒
                }
            }

            // 所有重试都失败
            if (empty($response)) {
                trace("支付宝授权最终请求失败，app_auth_code: " . $app_auth_code . ", 最后错误: " . $lastError, "error");
                return ['success' => false, 'message' => '网络请求失败，已重试' . $maxRetries . '次'];
            }

            $responseData = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                trace("支付宝授权响应JSON解析失败: " . json_last_error_msg() . ", 原始响应: " . $response, "error");
                return ['success' => false, 'message' => '响应数据格式错误'];
            }

            // 记录请求和响应日志
            trace("支付宝授权请求成功，app_auth_code: " . $app_auth_code, "info");
            trace("支付宝授权响应: " . $response, "info");

            // 处理响应结果
            if (isset($responseData['alipay_open_auth_token_app_response'])) {
                $tokenResponse = $responseData['alipay_open_auth_token_app_response'];

                if (isset($tokenResponse['code']) && $tokenResponse['code'] == '10000') {
                    // 验证必要字段是否存在
                    $requiredFields = ['app_auth_token', 'app_refresh_token', 'auth_app_id', 'expires_in', 're_expires_in'];
                    foreach ($requiredFields as $field) {
                        if (!isset($tokenResponse[$field])) {
                            trace("支付宝授权响应缺少必要字段: " . $field, "error");
                            return ['success' => false, 'message' => '授权响应数据不完整'];
                        }
                    }

                    // 授权成功
                    return [
                        'success' => true,
                        'data' => [
                            'app_auth_token' => $tokenResponse['app_auth_token'],
                            'app_refresh_token' => $tokenResponse['app_refresh_token'],
                            'auth_app_id' => $tokenResponse['auth_app_id'],
                            'expires_in' => $tokenResponse['expires_in'],
                            're_expires_in' => $tokenResponse['re_expires_in'],
                            'user_id' => isset($tokenResponse['user_id']) ? $tokenResponse['user_id'] : ''
                        ]
                    ];
                } else {
                    // 授权失败
                    $error_code = isset($tokenResponse['code']) ? $tokenResponse['code'] : 'unknown';
                    $error_msg = isset($tokenResponse['sub_msg']) ? $tokenResponse['sub_msg'] : '获取授权token失败';
                    trace("支付宝授权失败，错误码: " . $error_code . ", 错误信息: " . $error_msg, "error");
                    return ['success' => false, 'message' => $error_msg];
                }
            } else {
                trace("支付宝授权响应格式异常，缺少alipay_open_auth_token_app_response节点", "error");
                return ['success' => false, 'message' => '支付宝接口响应异常'];
            }

        } catch (Exception $e) {
            trace("获取app_auth_token异常: " . $e->getMessage(), "error");
            return ['success' => false, 'message' => '系统异常: ' . $e->getMessage()];
        }
    }

    /**
     * 获取应用配置信息
     * 使用方案3：默认配置
     */
    private function getAppConfig($app_id = null)
    {
        try {
            // 使用默认配置
            // 注意：实际使用时请替换为真实的配置
            return [
                'appid' => '****************', // 请替换为实际的应用ID
                'private_key' => 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOkh2sleb4sfplsyy1HSBXcwicqtFh4YTS6kwK6TRDODf1Jo3g3cTEIJuoavKKtFyg2470Sb8Kb9282Z456BvsNz42L+K1G4+04QnAdgA+ZlFsBr12Ckwir5WbFTYfJbOyX4LQp9qMIko7O4ckQASe6GKECtGLUBdcPoa/c9YfDosv6h21S67cZFxWEJ9Erx4pMOSyLVgZzLh9y/sJLnk+GxUKTQak8OgqMQz+IR+19X+cufGSSRrN/Nj8kE6ncTzmjJxfDB0ziJLMR7ZFwjICZhrQ/zikwZhp66sf8M6foMYpasemss9np6jh4TEGF4XoTdkjc19KGLWRoGfe28gBAgMBAAECggEAFGABaabKqYQV+u7OVtvgwV6pPmqTHd/y8YmWcIC/fVPNe3WVFSvccQMP+9O88eGw0zRNi8/2Q3GVSOX43OG1C98hPvE8/xD/SqRWlnDGvCQ3Qq776KC0HMjnIpC3eWAT/Ev2EAfNDfXgkfKB78ZOYr9nROOe6r/5dq9g5n+RfEKJHkCH7WRQAaBn7Rb2Euv3e6gr+a48sJWXZ/JSpO6atY4gl8ru3gJ8f5HBtyx+JY0FM4HrgQvgzKCJwoDcUDn/omkMuEKbgtLDxgahZkU2Cgh0DOAp05d9fK536JRP+AEJxxZm15n9/yW3qdF8QfiLuZ0LrsGkBlo2r9540nr6AQKBgQD8D+/c4KvKWEc6fl92Ry8a3n+6OiDe+8FmgxlmFxEL/tIDew0CszAirmAO808Xpn2R1jvdvmYo8cnrVxNYD76RO+XLaPAmbJnyw+7rKS2T/vlzLuHSiXBbHSpt3yuyZEtfYaad2K2R8R99ER4GlJ54CcuYa88mostuBFLZUObvUQKBgQCQzEs1WlSiX0H9OvDeX+W8II3coPzUHQ1z1H5cHRpSEu23zO0Rv8Bf97yfHfKhgZQrpIOuLXIsZLezdBuiwpohIdvrqDqVRNHoxWzDGxZ+p+aArMTSMA3qlfQd9J1McDj7MdAjFK45HP9EC40nZZai/j+iaRrvINFE3v8lrfEBsQKBgQDXQEzX1M0fiO+a1hxzhyqEyYlN4SH9CgUIo9/mvMAD/GIgMs7ThW4Pz9WcLs9WJQmKpuaM4HGheYmzfbK5OwjEQeBlKoIabhexpvy6uXmArvcbE+gowNHAHVIEbdaFCv0KwPtmRIlE3iLDzQBVIkmJvOOukDJgjhknJG4TwvDTYQKBgGqdcfcL1utjllQdlQM3uxFlKu87vk10QtZ5rLyoVRv/gfdmtVvvQ3emEsVaKK/QmEf2L1sNbVbQpg5FPKAAB7rDkomW6ePEs4Zf73BNPqfteqReEeHtriF/Xiq8VSHWM66JhAaAkLPO7QmuM7XBcEP9Tt+H4mQWVuAODfIxfBZRAoGBAOB7Mr1IMEcGYj3GLx8NF6OPV2Tp1MdK1N4p+3Ce4CGbZqdNLHoK1R/WOWJJ+R3Hw8cOXdP96qxPYghQkVAZ6atAYOuadCGZDu+Bu7f8rSH5zgR9DQMFwmPOkFQXcHN8fKcGqVkQxq2h+l9jMz1kLocXgqDJZf6S7tytTI74p727' // 请替换为实际的私钥
            ];

        } catch (Exception $e) {
            trace("获取应用配置异常: " . $e->getMessage(), "error");
            return false;
        }
    }

    /**
     * 保存授权信息到数据库
     * 使用方案2：根据回调参数中的state来判断是哪个账号
     */
    private function saveAuthInfo($authData, $decodedState)
    {
        try {
            // 记录解码后的 state 参数
            trace("解码后的state参数: " . $decodedState, "info");

            // 根据解码后的 state 参数获取账号信息
            $account = db('account')->where('account_identity', $decodedState)->find();
            if (!$account) {
                trace("根据解码后的state参数未找到对应账号，decodedState: " . $decodedState, "error");
                return false;
            }

            // 更新到账号表的config字段
            $config = [];
            if (!empty($account['config'])) {
                $config = json_decode($account['config'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    trace("账号config字段JSON解析失败: " . json_last_error_msg() . ", account_id: " . $account['id'], "error");
                    $config = []; // 使用空数组作为默认值
                }
            }

            $config['app_auth_token'] = $authData['app_auth_token'];
            $config['app_refresh_token'] = $authData['app_refresh_token'];
            $config['auth_app_id'] = $authData['auth_app_id'];
            $config['token_expires_in'] = $authData['expires_in'];
            $config['token_re_expires_in'] = $authData['re_expires_in'];
            $config['token_create_time'] = time();
            $config['user_id'] = $authData['user_id'] ?? '';

            try {
                $configJson = json_encode($config, JSON_THROW_ON_ERROR);
                $updateResult = db('account')->where('id', $account['id'])->update([
                    'config' => $configJson
                ]);
            } catch (Exception $e) {
                trace("JSON编码失败: " . $e->getMessage(), "error");
                return false;
            }

            if ($updateResult) {
                trace("更新账号授权配置成功，account_id: " . $account['id'] . ", decodedState: " . $decodedState, "info");
                return true;
            } else {
                trace("更新账号授权配置失败，account_id: " . $account['id'] . ", decodedState: " . $decodedState, "error");
                return false;
            }

        } catch (Exception $e) {
            trace("保存授权信息异常: " . $e->getMessage(), "error");
            return false;
        }
    }

    /**
     * 刷新 app_auth_token
     * 当 app_auth_token 过期时使用
     * 根据账号标识刷新授权token
     */
    public function refreshAppAuthToken($account_identity)
    {
        try {
            if (empty($account_identity)) {
                return ['success' => false, 'message' => 'account_identity 不能为空'];
            }

            // 根据账号标识获取账号信息
            $account = db('account')->where('account_identity', $account_identity)->find();
            if (!$account) {
                return ['success' => false, 'message' => '账号不存在'];
            }

            // 获取账号配置中的授权信息
            $config = json_decode($account['config'], true) ?: [];
            if (empty($config['app_refresh_token'])) {
                return ['success' => false, 'message' => '缺少 app_refresh_token，请重新授权'];
            }

            // 获取应用配置
            $appConfig = $this->getAppConfig();
            if (!$appConfig) {
                return ['success' => false, 'message' => '获取应用配置失败'];
            }

            $uri = 'https://openapi.alipay.com/gateway.do';

            // 构建刷新请求参数
            $requestConfigs = array(
                'grant_type' => 'refresh_token',
                'refresh_token' => $config['app_refresh_token']
            );

            $commonConfigs = array(
                'app_id' => $appConfig['appid'],
                'method' => 'alipay.open.auth.token.app',
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '1.0',
                'biz_content' => json_encode($requestConfigs),
            );

            // 使用 AlipayService 生成签名
            $aliPay = new AlipayService();
            $aliPay->setAppid($appConfig['appid']);
            $aliPay->setRsaPrivateKey($appConfig['private_key']);
            $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);

            // 构建请求字符串
            $requestStr = $aliPay->buildOrderStr($commonConfigs);

            // 带重试机制的网络请求
            $maxRetries = 3;
            $retryDelay = 1000; // 毫秒
            $retryCount = 0;
            $lastError = null;
            $response = null;

            while ($retryCount < $maxRetries) {
                try {
                    $response = Http::post($uri, $requestStr);
                    
                    // 检查HTTP请求是否成功
                    if (!empty($response)) {
                        break; // 请求成功，跳出重试循环
                    }
                    
                    $lastError = '无响应数据';
                    trace("刷新app_auth_token请求失败（尝试 " . ($retryCount + 1) . "/{$maxRetries}）: 无响应数据，account_identity: " . $account_identity, "error");
                    
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    trace("刷新app_auth_token请求异常（尝试 " . ($retryCount + 1) . "/{$maxRetries}）: " . $e->getMessage() . ", account_identity: " . $account_identity, "error");
                }
                
                $retryCount++;
                if ($retryCount < $maxRetries) {
                    usleep($retryDelay * 1000); // 转换为微秒
                }
            }

            // 所有重试都失败
            if (empty($response)) {
                trace("刷新app_auth_token最终请求失败，account_identity: " . $account_identity . ", 最后错误: " . $lastError, "error");
                return ['success' => false, 'message' => '网络请求失败，已重试' . $maxRetries . '次'];
            }

            $responseData = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                trace("刷新app_auth_token响应JSON解析失败: " . json_last_error_msg() . ", 原始响应: " . $response, "error");
                return ['success' => false, 'message' => '响应数据格式错误'];
            }

            // 记录日志
            trace("刷新app_auth_token请求成功，account_identity: " . $account_identity, "info");
            trace("刷新app_auth_token响应: " . $response, "info");

            // 处理响应
            if (isset($responseData['alipay_open_auth_token_app_response'])) {
                $tokenResponse = $responseData['alipay_open_auth_token_app_response'];

                if (isset($tokenResponse['code']) && $tokenResponse['code'] == '10000') {
                    // 刷新成功，更新账号配置
                    $config['app_auth_token'] = $tokenResponse['app_auth_token'];
                    $config['app_refresh_token'] = $tokenResponse['app_refresh_token'];
                    $config['token_expires_in'] = $tokenResponse['expires_in'];
                    $config['token_re_expires_in'] = $tokenResponse['re_expires_in'];
                    $config['token_refresh_time'] = time();

                    $updateResult = db('account')->where('id', $account['id'])->update([
                        'config' => json_encode($config, JSON_THROW_ON_ERROR)
                    ]);

                    if ($updateResult) {
                        trace("刷新授权token成功，account_id: " . $account['id'], "info");
                        return [
                            'success' => true,
                            'data' => [
                                'app_auth_token' => $tokenResponse['app_auth_token'],
                                'app_refresh_token' => $tokenResponse['app_refresh_token'],
                                'expires_in' => $tokenResponse['expires_in'],
                                're_expires_in' => $tokenResponse['re_expires_in']
                            ]
                        ];
                    } else {
                        return ['success' => false, 'message' => '保存刷新后的授权信息失败'];
                    }
                } else {
                    $error_msg = isset($tokenResponse['sub_msg']) ? $tokenResponse['sub_msg'] : '刷新授权token失败';
                    return ['success' => false, 'message' => $error_msg];
                }
            } else {
                return ['success' => false, 'message' => '支付宝接口响应异常'];
            }

        } catch (Exception $e) {
            trace("刷新app_auth_token异常: " . $e->getMessage(), "error");
            return ['success' => false, 'message' => '系统异常: ' . $e->getMessage()];
        }
    }

    /**
     * 检查授权状态
     * 公开接口，用于检查指定账号的授权状态
     */
    public function checkAuthStatus()
    {
        try {
            $account_identity = input('account_identity');

            if (empty($account_identity)) {
                $this->error('缺少账号标识参数');
            }

            // 获取账号信息
            $account = db('account')->where('account_identity', $account_identity)->find();
            if (!$account) {
                $this->error('账号不存在');
            }

            // 解析配置信息
            $config = [];
            if (!empty($account['config'])) {
                $config = json_decode($account['config'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error('账号配置数据格式错误');
                }
            }

            // 检查是否有授权信息
            if (empty($config['app_auth_token'])) {
                $this->success('未授权', [
                    'status' => 'not_authorized',
                    'message' => '该账号尚未进行支付宝第三方应用授权'
                ]);
                return;
            }

            // 验证app_auth_token是否有效
            $isTokenValid = $this->validateAppAuthToken($config['app_auth_token']);
            
            if (!$isTokenValid) {
                // token无效，清空相关配置信息
                unset($config['app_auth_token']);
                unset($config['app_refresh_token']);
                unset($config['auth_app_id']);
                unset($config['token_create_time']);
                unset($config['token_refresh_time']);
                unset($config['token_expires_in']);
                unset($config['token_re_expires_in']);
                
                // 更新数据库
                db('account')->where('id', $account['id'])->update([
                    'config' => json_encode($config, JSON_THROW_ON_ERROR)
                ]);
                
                $this->success('未授权', [
                    'status' => 'not_authorized',
                    'message' => '授权token无效，已清空授权信息'
                ]);
                return;
            }

            // token有效
            $this->success('授权状态检查完成', [
                'status' => 'valid',
                'message' => '授权有效',
                'auth_app_id' => $config['auth_app_id'] ?? '',
                'token_create_time' => isset($config['token_create_time']) ? date('Y-m-d H:i:s', $config['token_create_time']) : '',
                'token_refresh_time' => isset($config['token_refresh_time']) ? date('Y-m-d H:i:s', $config['token_refresh_time']) : '',
                'expires_in' => $config['token_expires_in'] ?? 0
            ]);

        } catch (Exception $e) {
            trace("检查授权状态异常: " . $e->getMessage(), "error");
            $this->error('系统异常: ' . $e->getMessage());
        }
    }

    /**
     * 验证app_auth_token是否有效
     */
    private function validateAppAuthToken($appAuthToken)
    {
        try {
            // 基础格式检查
            if (empty($appAuthToken) || strlen($appAuthToken) < 10) {
                return false;
            }

            // 使用固定的应用配置（参考accountlog方法）
            $appid = "****************";
            $private_key = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOkh2sleb4sfplsyy1HSBXcwicqtFh4YTS6kwK6TRDODf1Jo3g3cTEIJuoavKKtFyg2470Sb8Kb9282Z456BvsNz42L+K1G4+04QnAdgA+ZlFsBr12Ckwir5WbFTYfJbOyX4LQp9qMIko7O4ckQASe6GKECtGLUBdcPoa/c9YfDosv6h21S67cZFxWEJ9Erx4pMOSyLVgZzLh9y/sJLnk+GxUKTQak8OgqMQz+IR+19X+cufGSSRrN/Nj8kE6ncTzmjJxfDB0ziJLMR7ZFwjICZhrQ/zikwZhp66sf8M6foMYpasemss9np6jh4TEGF4XoTdkjc19KGLWRoGfe28gBAgMBAAECggEAFGABaabKqYQV+u7OVtvgwV6pPmqTHd/y8YmWcIC/fVPNe3WVFSvccQMP+9O88eGw0zRNi8/2Q3GVSOX43OG1C98hPvE8/xD/SqRWlnDGvCQ3Qq776KC0HMjnIpC3eWAT/Ev2EAfNDfXgkfKB78ZOYr9nROOe6r/5dq9g5n+RfEKJHkCH7WRQAaBn7Rb2Euv3e6gr+a48sJWXZ/JSpO6atY4gl8ru3gJ8f5HBtyx+JY0FM4HrgQvgzKCJwoDcUDn/omkMuEKbgtLDxgahZkU2Cgh0DOAp05d9fK536JRP+AEJxxZm15n9/yW3qdF8QfiLuZ0LrsGkBlo2r9540nr6AQKBgQD8D+/c4KvKWEc6fl92Ry8a3n+6OiDe+8FmgxlmFxEL/tIDew0CszAirmAO808Xpn2R1jvdvmYo8cnrVxNYD76RO+XLaPAmbJnyw+7rKS2T/vlzLuHSiXBbHSpt3yuyZEtfYaad2K2R8R99ER4GlJ54CcuYa88mostuBFLZUObvUQKBgQCQzEs1WlSiX0H9OvDeX+W8II3coPzUHQ1z1H5cHRpSEu23zO0Rv8Bf97yfHfKhgZQrpIOuLXIsZLezdBuiwpohIdvrqDqVRNHoxWzDGxZ+p+aArMTSMA3qlfQd9J1McDj7MdAjFK45HP9EC40nZZai/j+iaRrvINFE3v8lrfEBsQKBgQDXQEzX1M0fiO+a1hxzhyqEyYlN4SH9CgUIo9/mvMAD/GIgMs7ThW4Pz9WcLs9WJQmKpuaM4HGheYmzfbK5OwjEQeBlKoIabhexpvy6uXmArvcbE+gowNHAHVIEbdaFCv0KwPtmRIlE3iLDzQBVIkmJvOOukDJgjhknJG4TwvDTYQKBgGqdcfcL1utjllQdlQM3uxFlKu87vk10QtZ5rLyoVRv/gfdmtVvvQ3emEsVaKK/QmEf2L1sNbVbQpg5FPKAAB7rDkomW6ePEs4Zf73BNPqfteqReEeHtriF/Xiq8VSHWM66JhAaAkLPO7QmuM7XBcEP9Tt+H4mQWVuAODfIxfBZRAoGBAOB7Mr1IMEcGYj3GLx8NF6OPV2Tp1MdK1N4p+3Ce4CGbZqdNLHoK1R/WOWJJ+R3Hw8cOXdP96qxPYghQkVAZ6atAYOuadCGZDu+Bu7f8rSH5zgR9DQMFwmPOkFQXcHN8fKcGqVkQxq2h+l9jMz1kLocXgqDJZf6S7tytTI74p727";

            $uri = 'https://openapi.alipay.com/gateway.do';
            $start_time = date("Y-m-d H:i:s");
            
            // 构建请求参数 - 使用查询授权信息接口验证token
            $requestConfigs = array(
                'app_auth_token' => $appAuthToken
            );

            $commonConfigs = array(
                'app_id' => $appid,
                'method' => 'alipay.open.auth.token.app.query', // 查询授权信息接口
                'format' => 'JSON',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'timestamp' => $start_time,
                'version' => '1.0',
                'biz_content' => json_encode($requestConfigs),
            );

            // 使用 AlipayService 生成签名（参考accountlog方法）
            $aliPay = new AlipayService();
            $aliPay->setAppid($appid);
            $aliPay->setRsaPrivateKey($private_key);
            $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);
            $x = $aliPay->buildOrderStr($commonConfigs);
            
            // 带重试机制的网络请求
            $maxRetries = 3;
            $retryDelay = 1000; // 毫秒
            $retryCount = 0;
            $lastError = null;
            $xxx = null;

            while ($retryCount < $maxRetries) {
                try {
                    $xxx = Http::get("{$uri}?$x");
                    
                    // 检查HTTP请求是否成功
                    if (!empty($xxx)) {
                        break; // 请求成功，跳出重试循环
                    }
                    
                    $lastError = '无响应数据';
                    trace("验证app_auth_token请求失败（尝试 " . ($retryCount + 1) . "/{$maxRetries}）: 无响应数据", "error");
                    
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    trace("验证app_auth_token请求异常（尝试 " . ($retryCount + 1) . "/{$maxRetries}）: " . $e->getMessage(), "error");
                }
                
                $retryCount++;
                if ($retryCount < $maxRetries) {
                    usleep($retryDelay * 1000); // 转换为微秒
                }
            }

            // 所有重试都失败
            if (empty($xxx)) {
                trace("验证app_auth_token最终请求失败，最后错误: " . $lastError, "error");
                return false;
            }

            $jsondata = json_decode($xxx, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                trace("验证app_auth_token响应JSON解析失败: " . json_last_error_msg() . ", 原始响应: " . $xxx, "error");
                return false;
            }

            // 检查响应结果
            if (isset($jsondata['alipay_open_auth_token_app_query_response'])) {
                $queryResponse = $jsondata['alipay_open_auth_token_app_query_response'];

                if (isset($queryResponse['code']) && $queryResponse['code'] == '10000') {
                    // token有效
                    trace("app_auth_token验证成功", "info");
                    return true;
                } else {
                    // token无效
                    $error_msg = isset($queryResponse['sub_msg']) ? $queryResponse['sub_msg'] : 'token验证失败';
                    trace("app_auth_token验证失败: " . $error_msg, "error");
                    return false;
                }
            } else {
                trace("验证app_auth_token响应格式异常: " . json_encode($jsondata), "error");
                return false;
            }

        } catch (\Exception $e) {
            trace("验证app_auth_token异常: " . $e->getMessage(), "error");
            return false;
        }
    }
}
