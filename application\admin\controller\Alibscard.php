<?php

namespace app\admin\controller;

use AlipayService;
use app\admin\model\Merchants;
use app\admin\model\Order;
use app\common\controller\Backend;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Http;
use GuzzleHttp\Client;

/**
 * 支付宝名片码
 *
 * @icon fa fa-circle-o
 */
class Alibscard extends Backend
{
    protected $dataLimit = "auth";

    protected $dataLimitField = "admin_id";

    protected $noNeedRight = ['*'];

    /**
     * Multi方法可批量修改的字段
     */
    protected $multiFields = 'status,maximum,pay_limit,nopay_limit';
    
    /**
     * Account模型对象
     * @var \app\admin\model\Account
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Account;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where('channel','9010')
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
				$row->getRelation('admin')->visible(['nickname']);
                $row['today'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->sum('amount');
                $row['today'] = number_format($row['today'],2);
                $row['yesterday'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'yesterday')
                    ->sum('amount');
                $row['yesterday'] = number_format($row['yesterday'],2);
                $row['beforeday'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->sum('amount');
                $row['beforeday'] = number_format($row['beforeday'],2);
                $ispay = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->count();
                $all = Order::where('account_identity',$row['account_identity'])
                    ->whereTime('create_time', 'today')
                    ->count();
                $row['today_rate'] =  $all==0 ? "0" : number_format(($ispay/$all) * 100,2);
            }
            unset($row);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        // 计算账号统计数据
        $adminIds = $this->getDataLimitAdminIds();

        // 在线账号数量（正常状态且无异常信息）
        $onlineQuery = $this->model->where('channel','9010')
            ->where('status', 'normal')
            ->where(function($query) {
                $query->where('statusinfo', '')->whereOr('statusinfo', null);
            });
        if (is_array($adminIds)) {
            $onlineQuery->where($this->dataLimitField, 'in', $adminIds);
        }
        $onlineCount = $onlineQuery->count();

        // 离线账号数量（禁用状态或有异常信息）
        $offlineQuery = $this->model->where('channel','9010')
            ->where(function($query) {
                $query->where('status', 'hidden')
                      ->whereOr(function($subQuery) {
                          $subQuery->where('statusinfo', '<>', '')
                                   ->where('statusinfo', 'not null');
                      });
            });
        if (is_array($adminIds)) {
            $offlineQuery->where($this->dataLimitField, 'in', $adminIds);
        }
        $offlineCount = $offlineQuery->count();

        // 传递统计数据到视图
        $this->view->assign('onlineCount', $onlineCount);
        $this->view->assign('offlineCount', $offlineCount);

        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');

        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        
        // 自动生成唯一的account_identity
        do {
            $params['account_identity'] = 'DD' . date('Ymd') . substr(time(), -4) . sprintf('%03d', rand(0, 999));
        } while ($this->model->where('account_identity', $params['account_identity'])->find() != null);
        
        // 检查链接是否已经存在
        if (!empty($params['game_url'])) {
            // 获取所有相同渠道的账号
            $existingAccounts = $this->model->select();
            foreach ($existingAccounts as $account) {
                $config = json_decode($account['config'], true);
                if (isset($config['game_url']) && $config['game_url'] === $params['game_url']) {
                    $this->error("该链接已存在");
                }
            }
        }
        
        $params['status'] = 'hidden';
        // 格式化配置信息
        $params['config'] = json_encode([
            'game_url' => $params['game_url'],
        ], JSON_THROW_ON_ERROR);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        // 返回新创建的账号ID和账号标识
        $this->success('添加成功', '',[
            'id' => $this->model->id,
            'account_identity' => $params['account_identity']
        ]);
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            // 格式化配置信息
            $row['config'] = json_decode($row['config'], true);
            
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        
        // 检查链接是否已经存在（排除当前账号）
        if (!empty($params['game_url'])) {
            $existingAccounts = $this->model->where('id', '<>', $ids)->select();
            foreach ($existingAccounts as $account) {
                $config = json_decode($account['config'], true);
                if (isset($config['game_url']) && $config['game_url'] === $params['game_url']) {
                    $this->error("该链接已存在");
                }
            }
        }
        
        // 检查account_identity是否已经存在（排除当前账号）
        if (!empty($params['account_identity'])) {
            if ($this->model->where('account_identity', $params['account_identity'])->where('id', '<>', $ids)->find() != null) {
                $this->error("该账号标识已存在");
            }
        }
        
        // 格式化配置信息 - 保留原有配置，只更新game_url
        $existingConfig = json_decode($row['config'], true) ?: [];
        $existingConfig['game_url'] = $params['game_url'];
        $params['config'] = json_encode($existingConfig, JSON_THROW_ON_ERROR);
        unset($params['admin_id']);
        $params['statusinfo'] = '';
        $result = false;

        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $row = $this->model->get($ids);
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    public function test($ids)
    {
        if ($this->request->isPost()) {
            $ids = $ids ?: $this->request->post("ids");
            if (empty($ids)) {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $row = $this->model->where($pk,$ids)->find();
            $params = $this->request->post('row/a');

            $out_trade_no = \app\common\library\Order::createUniqueNo();
            $data = [
                'out_trade_no' => $out_trade_no,
                'merchants_code' => 'MC100020_15',
                'amount' => $params['price'],
                'channel_code' => $row["channel"],
                'notify_url' => 'haha',
                'test'=>'true',
                'account_id'=>$row['id']
            ];
            $sign = md5(urldecode($this->ascii($data)) . "&key=1a04a03e398dde1390e699ada41d4bd1");
            $url = $this->request->domain()."/api/order/newcreate?out_trade_no=".$out_trade_no."&merchants_code=MC100020_15&amount=".$data['amount']."&channel_code=".$data['channel_code']."&notify_url=haha&sign=".$sign."&test=true&account_id=".$row['id'];
            
            $res = Http::post($url);
            // halt($res);
            $res = json_decode($res,true);

            if ($res['code'] == 0){
                $this->error($res['msg']);
            }
            // $qr_url = $this->request->domain().'/api/demo/qrcode?codeText='.urlencode($res['data']);
            // $qr_url = 'https://qun.qq.com/qrcode/index?data='.urlencode($res['data']['url']);
            echo '<img src="'.$res['data']['qrcode'].'" alt="二维码" width=300px>';
            die;
            // return $this->redirect($res['data']['url']);
        }

        return $this->view->fetch();
    }
    
    protected function ascii($params = array()){
        unset($params['undefined']);
        unset($params['sign']);
        //ksort()对数组按照键名进行升序排序
        ksort($params);
        //reset()内部指针指向数组中的第一个元素
        reset($params);
        $str = http_build_query($params, '', '&');
        return $str;
    }
    

    /**
     * 批量更新
     *
     * @param $ids
     * @return void
     */
    public function multi($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $ids = $ids ?: $this->request->post('ids');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        if (false === $this->request->has('params')) {
            $this->error(__('No rows were updated'));
        }
        parse_str($this->request->post('params'), $values);
        $values = $this->auth->isSuperAdmin() ? $values : array_intersect_key($values, array_flip(is_array($this->multiFields) ? $this->multiFields : explode(',', $this->multiFields)));

        if (empty($values)) {
            $this->error(__('You have no permission'));
        }

        // 只有当status存在且为normal时才清空statusinfo
        if (isset($values['status']) && $values['status'] == 'normal'){
            $values['statusinfo'] = '';
        }
        
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $count = 0;
        Db::startTrans();
        try {
            $list = $this->model->where($this->model->getPk(), 'in', $ids)->select();
            foreach ($list as $item) {
                $count += $item->allowField(true)->isUpdate(true)->save($values);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were updated'));
    }

    public function paylimit($ids)
    {
        $ids = $ids ?: $this->request->post('ids');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $count = 0;
        Db::startTrans();
        try {
            $list = $this->model->where($this->model->getPk(), 'in', $ids)->select();
            foreach ($list as $item) {
                $count += $item->allowField(true)->isUpdate(true)->save(["pay_limit"=>$params['pay_limit']]);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were updated'));
    }

    public function accountlog($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $start_time = date("Y-m-01") . " 00:00:00";
        $end_time = date("Y-m-d")." 23:59:00";
        
        // 获取账号信息
        $account = db('account')->where('id', $ids)->find();
        $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);

        $uri = 'https://openapi.alipay.com/gateway.do';
        $requestConfigs = array(
            'start_time' => $start_time,
            'end_time' => $end_time
        );
        $appid = "****************";
        $private_key = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOkh2sleb4sfplsyy1HSBXcwicqtFh4YTS6kwK6TRDODf1Jo3g3cTEIJuoavKKtFyg2470Sb8Kb9282Z456BvsNz42L+K1G4+04QnAdgA+ZlFsBr12Ckwir5WbFTYfJbOyX4LQp9qMIko7O4ckQASe6GKECtGLUBdcPoa/c9YfDosv6h21S67cZFxWEJ9Erx4pMOSyLVgZzLh9y/sJLnk+GxUKTQak8OgqMQz+IR+19X+cufGSSRrN/Nj8kE6ncTzmjJxfDB0ziJLMR7ZFwjICZhrQ/zikwZhp66sf8M6foMYpasemss9np6jh4TEGF4XoTdkjc19KGLWRoGfe28gBAgMBAAECggEAFGABaabKqYQV+u7OVtvgwV6pPmqTHd/y8YmWcIC/fVPNe3WVFSvccQMP+9O88eGw0zRNi8/2Q3GVSOX43OG1C98hPvE8/xD/SqRWlnDGvCQ3Qq776KC0HMjnIpC3eWAT/Ev2EAfNDfXgkfKB78ZOYr9nROOe6r/5dq9g5n+RfEKJHkCH7WRQAaBn7Rb2Euv3e6gr+a48sJWXZ/JSpO6atY4gl8ru3gJ8f5HBtyx+JY0FM4HrgQvgzKCJwoDcUDn/omkMuEKbgtLDxgahZkU2Cgh0DOAp05d9fK536JRP+AEJxxZm15n9/yW3qdF8QfiLuZ0LrsGkBlo2r9540nr6AQKBgQD8D+/c4KvKWEc6fl92Ry8a3n+6OiDe+8FmgxlmFxEL/tIDew0CszAirmAO808Xpn2R1jvdvmYo8cnrVxNYD76RO+XLaPAmbJnyw+7rKS2T/vlzLuHSiXBbHSpt3yuyZEtfYaad2K2R8R99ER4GlJ54CcuYa88mostuBFLZUObvUQKBgQCQzEs1WlSiX0H9OvDeX+W8II3coPzUHQ1z1H5cHRpSEu23zO0Rv8Bf97yfHfKhgZQrpIOuLXIsZLezdBuiwpohIdvrqDqVRNHoxWzDGxZ+p+aArMTSMA3qlfQd9J1McDj7MdAjFK45HP9EC40nZZai/j+iaRrvINFE3v8lrfEBsQKBgQDXQEzX1M0fiO+a1hxzhyqEyYlN4SH9CgUIo9/mvMAD/GIgMs7ThW4Pz9WcLs9WJQmKpuaM4HGheYmzfbK5OwjEQeBlKoIabhexpvy6uXmArvcbE+gowNHAHVIEbdaFCv0KwPtmRIlE3iLDzQBVIkmJvOOukDJgjhknJG4TwvDTYQKBgGqdcfcL1utjllQdlQM3uxFlKu87vk10QtZ5rLyoVRv/gfdmtVvvQ3emEsVaKK/QmEf2L1sNbVbQpg5FPKAAB7rDkomW6ePEs4Zf73BNPqfteqReEeHtriF/Xiq8VSHWM66JhAaAkLPO7QmuM7XBcEP9Tt+H4mQWVuAODfIxfBZRAoGBAOB7Mr1IMEcGYj3GLx8NF6OPV2Tp1MdK1N4p+3Ce4CGbZqdNLHoK1R/WOWJJ+R3Hw8cOXdP96qxPYghQkVAZ6atAYOuadCGZDu+Bu7f8rSH5zgR9DQMFwmPOkFQXcHN8fKcGqVkQxq2h+l9jMz1kLocXgqDJZf6S7tytTI74p727";
        $commonConfigs = array(
            'app_id' => $appid,
            'method' => 'alipay.data.bill.accountlog.query',
            'format' => 'JSON',
            'charset'=> 'utf-8',
            'sign_type'=> 'RSA2',
            'timestamp'=> $start_time,
            'version'=> '1.0',
            'biz_content'=> json_encode($requestConfigs),
        );

        // 服务商模式：添加app_auth_token
        if (!empty($accountConfig['app_auth_token'])) {
            $commonConfigs['app_auth_token'] = $accountConfig['app_auth_token'];
        }

        $aliPay = new AlipayService();
        $aliPay->setAppid($appid);
        $aliPay->setRsaPrivateKey($private_key);
        $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);
        $x = $aliPay->buildOrderStr($commonConfigs);
        $xxx = Http::get("{$uri}?$x");
        $jsondata = json_decode($xxx,true);
        if(empty($jsondata['alipay_data_bill_accountlog_query_response']['detail_list'])){
            echo '账单为空,支付宝返回值如下：<br>';
            echo halt($jsondata);
        }
        $this->view->assign('data', $jsondata['alipay_data_bill_accountlog_query_response']['detail_list']);
        return $this->view->fetch();
    }
    
    /**
     * 商品管理
     */
    public function product($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }

        // 重定向到商品管理页面
        $this->redirect('account_product/index', ['account_id' => $ids]);
    }
}