<style>
    .admin-detail-container {
        padding: 20px;
    }
    .detail-section {
        margin-bottom: 30px;
    }
    .detail-section h4 {
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
        color: #333;
    }
    .admin-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    .info-item {
        display: flex;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
    }
    .info-label {
        font-weight: bold;
        color: #666;
        min-width: 80px;
        margin-right: 10px;
    }
    .info-value {
        color: #333;
        flex: 1;
    }
    .time-range-tabs {
        margin-bottom: 20px;
    }
    .time-range-tabs .btn {
        margin-right: 10px;
    }
    .stats-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    .stats-card {
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
    }
    .stats-card .stats-number {
        font-size: 24px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    .stats-card .stats-label {
        color: #7f8c8d;
        font-size: 14px;
    }
    .channel-stats-table {
        background: #fff;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
</style>

<div class="admin-detail-container">
    <!-- 基本信息区域 -->
    <div class="detail-section">
        <h4><i class="fa fa-user"></i> 管理员基本信息</h4>
        <div class="admin-info-grid" id="admin-info-grid">
            <!-- 基本信息将通过JavaScript动态填充 -->
        </div>
    </div>

    <!-- 收款统计区域 -->
    <div class="detail-section">
        <h4><i class="fa fa-bar-chart"></i> 收款统计</h4>
        
        <!-- 时间范围选择 -->
        <div class="time-range-tabs">
            <button type="button" class="btn btn-sm btn-primary time-range-btn active" data-range="today">今日</button>
            <button type="button" class="btn btn-sm btn-default time-range-btn" data-range="yesterday">昨日</button>
            <button type="button" class="btn btn-sm btn-default time-range-btn" data-range="week">本周</button>
            <button type="button" class="btn btn-sm btn-default time-range-btn" data-range="month">本月</button>
            <button type="button" class="btn btn-sm btn-default time-range-btn" data-range="last_month">上月</button>
            <button type="button" class="btn btn-sm btn-default time-range-btn" data-range="custom">自定义</button>
            <button type="button" class="btn btn-sm btn-default time-range-btn" data-range="all">全部</button>
        </div>

        <!-- 自定义日期范围选择 -->
        <div class="custom-date-range" id="custom-date-range" style="display: none; margin-bottom: 20px;">
            <div class="row">
                <div class="col-md-4">
                    <label>开始日期：</label>
                    <input type="date" class="form-control" id="start-date" />
                </div>
                <div class="col-md-4">
                    <label>结束日期：</label>
                    <input type="date" class="form-control" id="end-date" />
                </div>
                <div class="col-md-4">
                    <label>&nbsp;</label><br>
                    <button type="button" class="btn btn-success" id="apply-custom-range">应用</button>
                    <button type="button" class="btn btn-default" id="cancel-custom-range">取消</button>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-summary" id="stats-summary">
            <!-- 统计数据将通过JavaScript动态填充 -->
        </div>

        <!-- 按通道统计表格 -->
        <div class="channel-stats-table" style="position: relative;">
            <div class="loading-overlay" id="loading-overlay" style="display: none;">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
            </div>
            <table class="table table-striped table-bordered" id="channel-stats-table">
                <thead>
                    <tr>
                        <th>通道名称</th>
                        <th>通道编码</th>
                        <th>交易笔数</th>
                        <th>交易金额</th>
                        <th>成功率</th>
                    </tr>
                </thead>
                <tbody id="channel-stats-tbody">
                    <!-- 通道统计数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="hide layer-footer">
    <div class="col-xs-12 text-center">
        <button type="button" class="btn btn-primary btn-embossed btn-close" onclick="Layer.closeAll();">关闭</button>
    </div>
</div>

{include file="common/script" /}
