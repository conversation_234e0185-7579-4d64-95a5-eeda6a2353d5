<?php

namespace app\admin\model;

use think\Model;

class AccountProduct extends Model
{
    // 表名
    protected $name = 'account_product';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'price_text'
    ];

    public function getPriceTextAttr($value, $data)
    {
        return '¥' . number_format($data['price'], 2);
    }

    // 关联账号表
    public function account()
    {
        return $this->belongsTo('Account', 'account_id', 'id');
    }
}
