define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'auth/admin/index',
                    add_url: 'auth/admin/add',
                    edit_url: 'auth/admin/edit',
                    del_url: 'auth/admin/del',
                    multi_url: 'auth/admin/multi',
                }
            });

            var table = $("#table");

            //在表格内容渲染完成后回调的事件
            table.on('post-body.bs.table', function (e, json) {
                $("tbody tr[data-index]", this).each(function () {
                    if (parseInt($("td:eq(1)", this).text()) == Config.admin.id) {
                        $("input[type=checkbox]", this).prop("disabled", true);
                    }
                });
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                columns: [
                    [
                        {field: 'state', checkbox: true, },
                        {field: 'id', title: 'ID'},
                        {field: 'username', title: __('Username')},
                        {field: 'nickname', title: __('Nickname')},
                        {field: 'groups_text', title: __('Group'), operate:false, formatter: Table.api.formatter.label},
                        {field: 'today_amount', title: '今日收款', operate: false, formatter: function (v) {
                                return "<span class='btn btn-success'>¥" + (v || '0.00') + "</span>";
                            }},
                        {field: 'yesterday_amount', title: '昨日收款', operate: false, formatter: function (v) {
                                return "¥" + (v || '0.00');
                            }},
                        {field: 'total_amount', title: '总收款', operate: false, formatter: function (v) {
                                return "¥" + (v || '0.00');
                            }},
                        {field: 'status', title: __("Status"), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'logintime', title: __('Login time'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    // text: '详情',
                                    title: '查看详情',
                                    classname: 'btn btn-xs btn-info btn-click',
                                    icon: 'fa fa-eye',
                                    click: function (data, ret) {
                                        Fast.api.layer.open({
                                            type: 2,
                                            title: '管理员详情',
                                            shadeClose: true,
                                            shade: 0.8,
                                            maxmin: true,
                                            area: ['90%', '80%'],
                                            content: Fast.api.fixurl('auth/admin/detail') + '?ids=' + ret.ids
                                        });
                                    }
                                }
                            ],
                            formatter: function (value, row, index) {
                                if(row.id == Config.admin.id){
                                    return '';
                                }
                                return Table.api.formatter.operate.call(this, value, row, index);
                            }}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Form.api.bindevent($("form[role=form]"));
        },
        edit: function () {
            Form.api.bindevent($("form[role=form]"));
        },
        detail: function () {
            var adminId = Fast.api.query('ids');
            var currentTimeRange = 'today';

            // 时间格式化函数
            function formatDateTime(timestamp) {
                if (!timestamp) return '-';
                var date = new Date(timestamp * 1000);
                var year = date.getFullYear();
                var month = ('0' + (date.getMonth() + 1)).slice(-2);
                var day = ('0' + date.getDate()).slice(-2);
                var hours = ('0' + date.getHours()).slice(-2);
                var minutes = ('0' + date.getMinutes()).slice(-2);
                var seconds = ('0' + date.getSeconds()).slice(-2);
                return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            }

            // 初始化加载数据
            loadAdminDetail(currentTimeRange);

            // 时间范围切换事件
            $('.time-range-btn').on('click', function() {
                var timeRange = $(this).data('range');
                if (timeRange === currentTimeRange && timeRange !== 'custom') return;

                if (timeRange === 'custom') {
                    // 显示自定义日期选择器
                    $('#custom-date-range').show();
                    $('.time-range-btn').removeClass('btn-primary active').addClass('btn-default');
                    $(this).removeClass('btn-default').addClass('btn-primary active');
                } else {
                    // 隐藏自定义日期选择器
                    $('#custom-date-range').hide();
                    $('.time-range-btn').removeClass('btn-primary active').addClass('btn-default');
                    $(this).removeClass('btn-default').addClass('btn-primary active');

                    currentTimeRange = timeRange;
                    loadAdminDetail(timeRange);
                }
            });

            // 应用自定义日期范围
            $('#apply-custom-range').on('click', function() {
                var startDate = $('#start-date').val();
                var endDate = $('#end-date').val();

                if (!startDate || !endDate) {
                    Toastr.error('请选择开始日期和结束日期');
                    return;
                }

                if (startDate > endDate) {
                    Toastr.error('开始日期不能大于结束日期');
                    return;
                }

                currentTimeRange = 'custom';
                loadAdminDetail('custom', startDate, endDate);
            });

            // 取消自定义日期范围
            $('#cancel-custom-range').on('click', function() {
                $('#custom-date-range').hide();
                $('.time-range-btn').removeClass('btn-primary active').addClass('btn-default');
                $('.time-range-btn[data-range="today"]').removeClass('btn-default').addClass('btn-primary active');
                currentTimeRange = 'today';
                loadAdminDetail('today');
            });

            // 加载管理员详情数据
            function loadAdminDetail(timeRange, startDate, endDate) {
                $('#loading-overlay').show();

                var requestData = {
                    ids: adminId,
                    time_range: timeRange
                };

                // 如果是自定义时间范围，添加日期参数
                if (timeRange === 'custom' && startDate && endDate) {
                    requestData.start_date = startDate;
                    requestData.end_date = endDate;
                }

                Fast.api.ajax({
                    url: 'auth/admin/detail',
                    data: requestData
                }, function(data, ret) {
                    renderAdminInfo(data.admin_info);
                    renderStatsData(data.total_stats, data.channel_stats);
                    $('#loading-overlay').hide();
                }, function(data, ret) {
                    Toastr.error(ret.msg || '加载数据失败');
                    $('#loading-overlay').hide();
                });
            }

            // 渲染管理员基本信息
            function renderAdminInfo(adminInfo) {
                var html = '';
                var infoItems = [
                    {label: 'ID', value: adminInfo.id},
                    {label: '用户名', value: adminInfo.username},
                    {label: '昵称', value: adminInfo.nickname || '-'},
                    {label: '邮箱', value: adminInfo.email || '-'},
                    {label: '角色组', value: adminInfo.groups_text || '-'},
                    {label: '状态', value: adminInfo.status === 'normal' ? '<span class="label label-success">正常</span>' : '<span class="label label-danger">禁用</span>'},
                    {label: '创建时间', value: adminInfo.createtime ? formatDateTime(adminInfo.createtime) : '-'},
                    {label: '登录时间', value: adminInfo.logintime ? formatDateTime(adminInfo.logintime) : '-'}
                ];

                infoItems.forEach(function(item) {
                    html += '<div class="info-item">';
                    html += '<span class="info-label">' + item.label + ':</span>';
                    html += '<span class="info-value">' + item.value + '</span>';
                    html += '</div>';
                });

                $('#admin-info-grid').html(html);
            }

            // 渲染统计数据
            function renderStatsData(totalStats, channelStats) {
                // 渲染统计概览
                var summaryHtml = '';
                summaryHtml += '<div class="stats-card">';
                summaryHtml += '<div class="stats-number">' + (totalStats.total_orders || 0) + '</div>';
                summaryHtml += '<div class="stats-label">总交易笔数</div>';
                summaryHtml += '</div>';
                summaryHtml += '<div class="stats-card">';
                summaryHtml += '<div class="stats-number">¥' + (totalStats.total_amount || '0.00') + '</div>';
                summaryHtml += '<div class="stats-label">总交易金额</div>';
                summaryHtml += '</div>';
                summaryHtml += '<div class="stats-card">';
                summaryHtml += '<div class="stats-number">' + (totalStats.success_rate || '0.00') + '%</div>';
                summaryHtml += '<div class="stats-label">成功率</div>';
                summaryHtml += '</div>';

                $('#stats-summary').html(summaryHtml);

                // 渲染通道统计表格
                var tableHtml = '';
                if (channelStats && channelStats.length > 0) {
                    channelStats.forEach(function(stat) {
                        tableHtml += '<tr>';
                        tableHtml += '<td>' + (stat.channel_name || '-') + '</td>';
                        tableHtml += '<td>' + (stat.channel_code || '-') + '</td>';
                        tableHtml += '<td>' + (stat.order_count || 0) + '</td>';
                        tableHtml += '<td>¥' + (stat.total_amount || '0.00') + '</td>';
                        tableHtml += '<td>' + (stat.success_rate || '0.00') + '%</td>';
                        tableHtml += '</tr>';
                    });
                } else {
                    tableHtml = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
                }

                $('#channel-stats-tbody').html(tableHtml);
            }
        }
    };
    return Controller;
});

