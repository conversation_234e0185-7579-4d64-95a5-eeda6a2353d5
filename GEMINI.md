# 不要随便删除文件
# Gemini 项目背景: Tengpay

## 项目概述

本项目名为 “Tengpay”，是一个使用 PHP 构建的 Web 应用程序。它基于 **ThinkPHP 5** 框架，并围绕 **FastAdmin** 后台框架进行构建。项目中存在如 `cccyun/payment` 等支付相关的依赖，以及 `/cashier`、`/pay` 和 `/notify` 等路由，这些都强烈表明这是一个支付处理或支付网关系统。

- **后端技术:** PHP 7.2+
- **开发框架:** ThinkPHP 5.0.24
- **核心系统:** FastAdmin
- **依赖管理:** Composer

## 构建与运行

这是一个标准的 PHP 应用程序。您需要一个装有 PHP 的 Web 服务器环境（如 Apache 或 Nginx）来运行它。

### 1. 安装依赖

项目依赖由 Composer 管理。要安装这些依赖，请运行：

```bash
E:/phpstudy_pro/Extensions/php/php8.2.9nts/php.exe E:/phpstudy_pro/Extensions/php/php8.2.9nts/composer.phar install
```
- 用户的PHP可执行文件路径是 'E:/phpstudy_pro/Extensions/php/php8.2.9nts/php.exe'，Composer的路径是 'E:/phpstudy_pro/Extensions/php/php8.2.9nts/composer.phar'。

### 2. Web 服务器配置

Web 服务器的文档根目录（Document Root）应指向项目的 `public/` 目录，并启用 URL 重写功能，将所有请求都指向 `index.php`。

**Nginx 配置示例:**

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/your/project/public;
    index index.php index.html index.htm;

    location / {
        if (!-e $request_filename){
            rewrite  ^(.*)$  /index.php?s=$1  last;   break;
        }
    }

    location ~ \.php$ {
        fastcgi_pass   127.0.0.1:9000;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME  $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }
}
```

### 3. 运行命令

项目使用 ThinkPHP 的命令行工具。您可以通过 `think` 脚本来执行命令。

**示例:**

```bash
E:/phpstudy_pro/Extensions/php/php8.2.9nts/php.exe think
```

这将显示可用的命令列表，用于执行诸如清理缓存、管理数据库迁移以及与 FastAdmin 插件交互等任务。

## 开发规范

- **框架:** 遵循 ThinkPHP 5 和 FastAdmin 的约定和最佳实践。
- **插件:** 新功能应尽可能作为 FastAdmin 插件进行开发。插件位于 `/addons` 目录中。
- **配置:** 应用程序级别的配置位于 `application/config.php`。特定于环境的设置应放在项目根目录的 `.env` 文件中。
- **路由:** 应用程序的路由在 `application/route.php` 中定义。插件特定的路由通常由插件自行处理。
- **数据库:** 数据库配置位于 `application/database.php`。