<?php
namespace app\crontab\command;

ini_set ("memory_limit","-1");

use AlipayService;
use Psr\Http\Message\ResponseInterface;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use think\exception\DbException;
use think\Log;

class Task9007 extends Command
{
    // 并发数即为每批处理的订单数
    private $concurrency = 50; // 每批处理50个订单，同时50个并发

    // 超时配置
    private $requestTimeout = 5; // HTTP请求超时时间（秒）
    private $connectTimeout = 5; // 连接超时时间（秒）
    private $promiseTimeout = 60; // Promise等待超时时间（秒）
    
    protected function configure()
    {
        // php think Task9007
        $this->setName('Task9007')
            ->setDescription('task定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        while (true) {
            // 输出开始时间
//            $output->writeln("TaskCommand:start:time:".date('Y-m-d H:i:s'));
            $startTime = time();

            // 获取5分钟内的订单
            $orderList = db('order')
                ->where('pay_status', 0)
                // ->where('pay_url', 'not null')
                ->where('channel_code', 'in', ['9007'])
                ->whereTime('create_time', '-5 minutes')
                ->select();

//            for($i = 0; $i < 11; $i++) {
//                $orderList = array_merge($orderList,$orderList);
//            }
            $output->writeln(date('Y-m-d H:i:s')." - 获取5分钟内的订单:".count($orderList)."笔");

            if (count($orderList) > 0) {
                try {
                    $this->processOrdersInBatches($orderList, $output);
                } catch (Exception $e) {
                    $output->writeln('支付宝账单查询失败，异常信息：'.$e->getMessage());
                }
            }

            $output->writeln("TaskCommand:execute:time:".($endTime = time() - $startTime)."s");
            // 休眠5秒
            sleep(5);
        }
    }

    /**
     * 分批处理订单 - 每批订单数 = 并发数
     */
    protected function processOrdersInBatches($orderList, $output)
    {
        $totalOrders = count($orderList);
        $batches = array_chunk($orderList, $this->concurrency);
        $batchCount = count($batches);
        
        $output->writeln("=== 开始处理 {$totalOrders} 笔订单，分为 {$batchCount} 批，每批 {$this->concurrency} 个并发 ===");
        
        foreach ($batches as $batchIndex => $batch) {
            $batchNumber = $batchIndex + 1;
            $batchSize = count($batch);
            $batchStartTime = microtime(true);
            
            $output->writeln("处理第 {$batchNumber}/{$batchCount} 批，订单数: {$batchSize}");
            
            $this->alipayCheckBatch($batch, $output);
            
            $batchTime = round((microtime(true) - $batchStartTime) * 1000, 2);
            $output->writeln("  └── 批次完成，耗时: {$batchTime}ms");
            
            // 批次间短暂休息
            if ($batchNumber < $batchCount) {
                usleep(100000); // 0.1秒
            }
        }
        
        $output->writeln("=== 所有批次处理完成 ===");
    }

    /**
     * 处理单个批次的订单
     */
    protected function alipayCheckBatch($orderList, $output)
    {
        $client = new Client([
            'verify' => false,
            'timeout' => $this->requestTimeout,
            'connect_timeout' => $this->connectTimeout,
            'read_timeout' => $this->requestTimeout,
            'http_errors' => false // 不抛出HTTP错误异常，便于处理
        ]);
        $promises = [];
        
        foreach ($orderList as $order) {
            // 输出订单信息和当前支付状态
            $payStatusText = $order['pay_status'] == 1 ? '已完成' : '未支付';
            // $output->writeln("处理订单: {$order['out_trade_no']} | 金额: {$order['amount']} | 当前支付状态: {$payStatusText}");

            $start_time = datetime($order['create_time']);
            $end_time = datetime(time());

            // 获取账号信息
            $account = db('account')->where('account_identity', $order['account_identity'])->find();
            if(empty($account)) continue;
            
            $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);

            $uri = 'https://openapi.alipay.com/gateway.do';
            $requestConfigs = array(
                'start_time' => $start_time,
                'end_time' => $end_time,
            );
            $commonConfigs = array(
                'app_id' => '****************',
                'method' => 'alipay.data.bill.accountlog.query',
                'format' => 'JSON',
                'charset'=> 'utf-8',
                'sign_type'=> 'RSA2',
                'timestamp'=> $start_time,
                'version'=> '1.0',
                'biz_content'=> json_encode($requestConfigs),
            );

            if (!empty($accountConfig['app_auth_token'])) {
                $commonConfigs['app_auth_token'] = $accountConfig['app_auth_token'];
            }

            $aliPay = new AlipayService();
            $aliPay->setAppid('****************');
            $aliPay->setRsaPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOkh2sleb4sfplsyy1HSBXcwicqtFh4YTS6kwK6TRDODf1Jo3g3cTEIJuoavKKtFyg2470Sb8Kb9282Z456BvsNz42L+K1G4+04QnAdgA+ZlFsBr12Ckwir5WbFTYfJbOyX4LQp9qMIko7O4ckQASe6GKECtGLUBdcPoa/c9YfDosv6h21S67cZFxWEJ9Erx4pMOSyLVgZzLh9y/sJLnk+GxUKTQak8OgqMQz+IR+19X+cufGSSRrN/Nj8kE6ncTzmjJxfDB0ziJLMR7ZFwjICZhrQ/zikwZhp66sf8M6foMYpasemss9np6jh4TEGF4XoTdkjc19KGLWRoGfe28gBAgMBAAECggEAFGABaabKqYQV+u7OVtvgwV6pPmqTHd/y8YmWcIC/fVPNe3WVFSvccQMP+9O88eGw0zRNi8/2Q3GVSOX43OG1C98hPvE8/xD/SqRWlnDGvCQ3Qq776KC0HMjnIpC3eWAT/Ev2EAfNDfXgkfKB78ZOYr9nROOe6r/5dq9g5n+RfEKJHkCH7WRQAaBn7Rb2Euv3e6gr+a48sJWXZ/JSpO6atY4gl8ru3gJ8f5HBtyx+JY0FM4HrgQvgzKCJwoDcUDn/omkMuEKbgtLDxgahZkU2Cgh0DOAp05d9fK536JRP+AEJxxZm15n9/yW3qdF8QfiLuZ0LrsGkBlo2r9540nr6AQKBgQD8D+/c4KvKWEc6fl92Ry8a3n+6OiDe+8FmgxlmFxEL/tIDew0CszAirmAO808Xpn2R1jvdvmYo8cnrVxNYD76RO+XLaPAmbJnyw+7rKS2T/vlzLuHSiXBbHSpt3yuyZEtfYaad2K2R8R99ER4GlJ54CcuYa88mostuBFLZUObvUQKBgQCQzEs1WlSiX0H9OvDeX+W8II3coPzUHQ1z1H5cHRpSEu23zO0Rv8Bf97yfHfKhgZQrpIOuLXIsZLezdBuiwpohIdvrqDqVRNHoxWzDGxZ+p+aArMTSMA3qlfQd9J1McDj7MdAjFK45HP9EC40nZZai/j+iaRrvINFE3v8lrfEBsQKBgQDXQEzX1M0fiO+a1hxzhyqEyYlN4SH9CgUIo9/mvMAD/GIgMs7ThW4Pz9WcLs9WJQmKpuaM4HGheYmzfbK5OwjEQeBlKoIabhexpvy6uXmArvcbE+gowNHAHVIEbdaFCv0KwPtmRIlE3iLDzQBVIkmJvOOukDJgjhknJG4TwvDTYQKBgGqdcfcL1utjllQdlQM3uxFlKu87vk10QtZ5rLyoVRv/gfdmtVvvQ3emEsVaKK/QmEf2L1sNbVbQpg5FPKAAB7rDkomW6ePEs4Zf73BNPqfteqReEeHtriF/Xiq8VSHWM66JhAaAkLPO7QmuM7XBcEP9Tt+H4mQWVuAODfIxfBZRAoGBAOB7Mr1IMEcGYj3GLx8NF6OPV2Tp1MdK1N4p+3Ce4CGbZqdNLHoK1R/WOWJJ+R3Hw8cOXdP96qxPYghQkVAZ6atAYOuadCGZDu+Bu7f8rSH5zgR9DQMFwmPOkFQXcHN8fKcGqVkQxq2h+l9jMz1kLocXgqDJZf6S7tytTI74p727");
            $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);
            $x = $aliPay->buildOrderStr($commonConfigs);

            // 创建异步请求
            $promises[] = $client->getAsync("http://**************:3000/proxy.php?target_url=".urlencode("{$uri}?$x"), [
                'timeout' => $this->requestTimeout,
                'connect_timeout' => $this->connectTimeout
            ])
                ->then(function (Response $response) use ($order, $output) {
                    $result = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR)['alipay_data_bill_accountlog_query_response'];

                    if($result['code'] != 10000) {
                        $output->writeln('订单'.$order['out_trade_no'].'支付宝账单查询失败，返回信息：'.$result['msg']);
                        return;
                    }

                    $output->writeln('订单'.$order['out_trade_no'].'支付宝账单查询成功，返回信息：'.json_encode($result));
                    if (!empty($result['detail_list'])) {
                        foreach ($result['detail_list'] as $detail) {
                            if($detail['direction'] == "收入" && $detail['trans_memo']===$order['out_trade_no']) {
                                $od = db('order')->where('pay_trade_no', $detail['alipay_order_no'])->find();
                                if(empty($od)) {
                                    db('order')
                                        ->where('id', $order['id'])
                                        ->update(['pay_status' => 1, 'pay_trade_no' => $detail['alipay_order_no']]);

                                    $output->writeln("订单 {$order['out_trade_no']} 支付状态更新：未支付 → 已完成 | 支付订单号：{$detail['alipay_order_no']}");

                                    $merchants = db('merchants')->where('id', $order['merchants_id'])->find();
                                    if (\app\common\library\Order::merchantsCallback($order['callback_url'], $merchants['key'], $order['out_trade_no'], $order['amount'], $order['channel_code'], 1)) {
                                        db('order')
                                            ->where('id', $order['id'])
                                            ->update(['callback_status' => 1, 'callback_time' => time()]);
                                    } else {
                                        $output->writeln('【回调失败】 订单号：'.$order['out_trade_no']);
                                    }
                                }
                            }
                        }
                    }
                })
                ->otherwise(function (RequestException $reason) use ($order, $output) {
                    $errorMsg = $reason->getMessage();
                    // 检查是否为超时错误
                    if (strpos($errorMsg, 'timeout') !== false || strpos($errorMsg, 'timed out') !== false) {
                        $output->writeln('订单号：'.$order['out_trade_no'].'账号：'.$order['account_identity'].'请求超时，异常信息：'.$errorMsg);
                    } else {
                        $output->writeln('订单号：'.$order['out_trade_no'].'账号：'.$order['account_identity'].'发生未知异常，异常信息：'.$errorMsg);
                    }
                });
        }

        // 等待当前批次的所有请求完成，设置超时时间
        try {
            \GuzzleHttp\Promise\Utils::settle($promises)->wait($this->promiseTimeout);
        } catch (\Exception $e) {
            $output->writeln('批次处理超时或发生异常：' . $e->getMessage());
        }
    }

}