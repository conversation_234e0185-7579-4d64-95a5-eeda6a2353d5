<?php

namespace app\admin\controller\auth;

use app\admin\model\AuthGroup;
use app\admin\model\AuthGroupAccess;
use app\admin\model\Order;
use app\common\controller\Backend;
use fast\Random;
use fast\Tree;
use think\Db;
use think\Validate;

/**
 * 管理员管理
 *
 * @icon   fa fa-users
 * @remark 一个管理员可以有多个角色组,左侧的菜单根据管理员所拥有的权限进行生成
 */
class Admin extends Backend
{

    /**
     * @var \app\admin\model\Admin
     */
    protected $model = null;
    protected $selectpageFields = 'id,username,nickname,avatar';
    protected $searchFields = 'id,username,nickname';
    protected $childrenGroupIds = [];
    protected $childrenAdminIds = [];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('Admin');

        $this->childrenAdminIds = $this->auth->getChildrenAdminIds($this->auth->isSuperAdmin());
        $this->childrenGroupIds = $this->auth->getChildrenGroupIds($this->auth->isSuperAdmin());

        $groupList = collection(AuthGroup::where('id', 'in', $this->childrenGroupIds)->select())->toArray();

        Tree::instance()->init($groupList);
        $groupdata = [];
        if ($this->auth->isSuperAdmin()) {
            $result = Tree::instance()->getTreeList(Tree::instance()->getTreeArray(0));
            foreach ($result as $k => $v) {
                $groupdata[$v['id']] = $v['name'];
            }
        } else {
            $result = [];
            $groups = $this->auth->getGroups();
            foreach ($groups as $m => $n) {
                $childlist = Tree::instance()->getTreeList(Tree::instance()->getTreeArray($n['id']));
                $temp = [];
                foreach ($childlist as $k => $v) {
                    $temp[$v['id']] = $v['name'];
                }
                $result[__($n['name'])] = $temp;
            }
            $groupdata = $result;
        }

        $this->view->assign('groupdata', $groupdata);
        $this->assignconfig("admin", ['id' => $this->auth->id]);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $childrenGroupIds = $this->childrenGroupIds;
            $groupName = AuthGroup::where('id', 'in', $childrenGroupIds)
                ->column('id,name');
            $authGroupList = AuthGroupAccess::where('group_id', 'in', $childrenGroupIds)
                ->field('uid,group_id')
                ->select();

            $adminGroupName = [];
            foreach ($authGroupList as $k => $v) {
                if (isset($groupName[$v['group_id']])) {
                    $adminGroupName[$v['uid']][$v['group_id']] = $groupName[$v['group_id']];
                }
            }
            $groups = $this->auth->getGroups();
            foreach ($groups as $m => $n) {
                $adminGroupName[$this->auth->id][$n['id']] = $n['name'];
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->where('id', 'in', $this->childrenAdminIds)
                ->field(['password', 'salt', 'token'], true)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $k => &$v) {
                $groups = isset($adminGroupName[$v['id']]) ? $adminGroupName[$v['id']] : [];
                $v['groups'] = implode(',', array_keys($groups));
                $v['groups_text'] = implode(',', array_values($groups));
                
                // 添加收款统计
                $v['today_amount'] = \app\admin\model\Order::where('admin_id', $v['id'])
                    ->where('callback_status', 1)
                    ->whereTime('callback_time', 'today')
                    ->sum('amount');
                $v['today_amount'] = number_format($v['today_amount'], 2);
                
                $v['yesterday_amount'] = \app\admin\model\Order::where('admin_id', $v['id'])
                    ->where('callback_status', 1)
                    ->whereTime('callback_time', 'yesterday')
                    ->sum('amount');
                $v['yesterday_amount'] = number_format($v['yesterday_amount'], 2);
                
                $v['total_amount'] = \app\admin\model\Order::where('admin_id', $v['id'])
                    ->where('callback_status', 1)
                    ->sum('amount');
                $v['total_amount'] = number_format($v['total_amount'], 2);
            }
            unset($v);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 查看详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (!in_array($row->id, $this->childrenAdminIds)) {
            $this->error(__('You have no permission'));
        }

        if ($this->request->isAjax()) {
            // 获取时间范围参数
            $timeRange = $this->request->param('time_range', 'today');
            $startDate = $this->request->param('start_date', '');
            $endDate = $this->request->param('end_date', '');

            // 获取管理员基本信息
            $adminInfo = $row->toArray();

            // 调试信息
            \think\Log::write('管理员ID: ' . $row->id . ', 时间范围: ' . $timeRange . ', 开始日期: ' . $startDate . ', 结束日期: ' . $endDate, 'info');

            // 获取管理员角色组信息
            $grouplist = $this->auth->getGroups($row['id']);
            $groups = [];
            foreach ($grouplist as $group) {
                $groups[] = $group['name'];
            }
            $adminInfo['groups_text'] = implode(', ', $groups);

            // 构建时间查询条件
            \think\Log::write('开始构建时间查询条件', 'info');
            $timeWhere = $this->buildTimeWhere($timeRange, $startDate, $endDate);
            \think\Log::write('时间查询条件构建完成: ' . json_encode($timeWhere), 'info');

            // 先简化查询，只获取基本的通道统计
            \think\Log::write('开始查询通道统计', 'info');
            try {
                $orderQuery = \app\admin\model\Order::where('admin_id', $row->id)
                    ->where('callback_status', 1);

                // 添加时间条件
                if ($timeWhere) {
                    $orderQuery->where($timeWhere[0], $timeWhere[1], $timeWhere[2]);
                }

                $channelStats = $orderQuery->field([
                        'channel_code',
                        'COUNT(*) as order_count',
                        'SUM(amount) as total_amount'
                    ])
                    ->group('channel_code')
                    ->select();

                // 转换为数组
                if (is_object($channelStats)) {
                    $channelStats = $channelStats->toArray();
                } elseif (!is_array($channelStats)) {
                    $channelStats = [];
                }

                \think\Log::write('通道统计查询成功，数量: ' . count($channelStats), 'info');

            } catch (Exception $e) {
                \think\Log::write('通道统计查询失败: ' . $e->getMessage(), 'error');
                $channelStats = [];
            }

            // 单独计算成功订单数
            foreach ($channelStats as &$stat) {
                try {
                    $successQuery = \app\admin\model\Order::where('admin_id', $row->id)
                        ->where('callback_status', 1)
                        ->where('pay_status', 1)
                        ->where('channel_code', $stat['channel_code']);

                    // 添加时间条件
                    if ($timeWhere) {
                        $successQuery->where($timeWhere[0], $timeWhere[1], $timeWhere[2]);
                    }

                    $stat['success_count'] = $successQuery->count();
                } catch (Exception $e) {
                    \think\Log::write('成功订单数查询失败: ' . $e->getMessage(), 'error');
                    $stat['success_count'] = 0;
                }
            }

            // 获取通道名称
            $channelCodes = array_column($channelStats, 'channel_code');
            $channels = [];
            if (!empty($channelCodes)) {
                $channelList = \app\admin\model\Channel::where('code', 'in', $channelCodes)->select();
                foreach ($channelList as $channel) {
                    $channels[$channel['code']] = $channel['name'];
                }
            }

            // 为统计数据添加通道名称
            foreach ($channelStats as &$stat) {
                $stat['channel_name'] = isset($channels[$stat['channel_code']]) ?
                    $channels[$stat['channel_code']] : $stat['channel_code'];
            }

            // 计算总统计
            try {
                $totalQuery = \app\admin\model\Order::where('admin_id', $row->id)
                    ->where('callback_status', 1);

                // 添加时间条件
                if ($timeWhere) {
                    $totalQuery->where($timeWhere[0], $timeWhere[1], $timeWhere[2]);
                }

                $totalStats = $totalQuery->field([
                        'COUNT(*) as total_orders',
                        'SUM(amount) as total_amount'
                    ])
                    ->find();

                // 转换为数组并确保数据格式正确
                if (is_object($totalStats)) {
                    $totalStats = $totalStats->toArray();
                } elseif (!is_array($totalStats)) {
                    $totalStats = ['total_orders' => 0, 'total_amount' => 0];
                }

                // 单独计算成功订单数
                $successTotalQuery = \app\admin\model\Order::where('admin_id', $row->id)
                    ->where('callback_status', 1)
                    ->where('pay_status', 1);

                // 添加时间条件
                if ($timeWhere) {
                    $successTotalQuery->where($timeWhere[0], $timeWhere[1], $timeWhere[2]);
                }

                $totalStats['success_orders'] = $successTotalQuery->count();

                \think\Log::write('总统计查询成功: ' . json_encode($totalStats), 'info');

            } catch (Exception $e) {
                \think\Log::write('总统计查询失败: ' . $e->getMessage(), 'error');
                $totalStats = ['total_orders' => 0, 'total_amount' => 0, 'success_orders' => 0];
            }

            // 调试信息
            \think\Log::write('通道统计数量: ' . count($channelStats), 'info');
            \think\Log::write('总统计: ' . json_encode($totalStats), 'info');

            // 格式化数据
            foreach ($channelStats as &$stat) {
                $stat['total_amount'] = number_format($stat['total_amount'], 2);
                $stat['success_rate'] = $stat['order_count'] > 0 ?
                    number_format(($stat['success_count'] / $stat['order_count']) * 100, 2) : '0.00';
            }

            $totalStats['total_amount'] = number_format($totalStats['total_amount'] ?: 0, 2);
            $totalStats['success_rate'] = $totalStats['total_orders'] > 0 ?
                number_format(($totalStats['success_orders'] / $totalStats['total_orders']) * 100, 2) : '0.00';

            $result = [
                'admin_info' => $adminInfo,
                'channel_stats' => $channelStats,
                'total_stats' => $totalStats,
                'time_range' => $timeRange
            ];

            $this->success('获取数据成功', null, $result);
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a");
            if ($params) {
                Db::startTrans();
                try {
                    if (!Validate::is($params['password'], '\S{6,30}')) {
                        exception(__("Please input correct password"));
                    }
                    $params['salt'] = Random::alnum();
                    $params['password'] = md5(md5($params['password']) . $params['salt']);
                    $params['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
                    $result = $this->model->validate('Admin.add')->save($params);
                    if ($result === false) {
                        exception($this->model->getError());
                    }
                    $group = $this->request->post("group/a");

                    //过滤不允许的组别,避免越权
                    $group = array_intersect($this->childrenGroupIds, $group);
                    if (!$group) {
                        exception(__('The parent group exceeds permission limit'));
                    }

                    $dataset = [];
                    foreach ($group as $value) {
                        $dataset[] = ['uid' => $this->model->id, 'group_id' => $value];
                    }
                    model('AuthGroupAccess')->saveAll($dataset);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (!in_array($row->id, $this->childrenAdminIds)) {
            $this->error(__('You have no permission'));
        }
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a");
            if ($params) {
                Db::startTrans();
                try {
                    if ($params['password']) {
                        if (!Validate::is($params['password'], '\S{6,30}')) {
                            exception(__("Please input correct password"));
                        }
                        $params['salt'] = Random::alnum();
                        $params['password'] = md5(md5($params['password']) . $params['salt']);
                    } else {
                        unset($params['password'], $params['salt']);
                    }
                    //这里需要针对username和email做唯一验证
                    $adminValidate = \think\Loader::validate('Admin');
                    $adminValidate->rule([
                        'username' => 'require|regex:\w{3,30}|unique:admin,username,' . $row->id,
                        'email'    => 'require|email|unique:admin,email,' . $row->id,
                        'mobile'    => 'regex:1[3-9]\d{9}|unique:admin,mobile,' . $row->id,
                        'password' => 'regex:\S{32}',
                    ]);
                    $result = $row->validate('Admin.edit')->save($params);
                    if ($result === false) {
                        exception($row->getError());
                    }

                    // 先移除所有权限
                    model('AuthGroupAccess')->where('uid', $row->id)->delete();

                    $group = $this->request->post("group/a");

                    // 过滤不允许的组别,避免越权
                    $group = array_intersect($this->childrenGroupIds, $group);
                    if (!$group) {
                        exception(__('The parent group exceeds permission limit'));
                    }

                    $dataset = [];
                    foreach ($group as $value) {
                        $dataset[] = ['uid' => $row->id, 'group_id' => $value];
                    }
                    model('AuthGroupAccess')->saveAll($dataset);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $grouplist = $this->auth->getGroups($row['id']);
        $groupids = [];
        foreach ($grouplist as $k => $v) {
            $groupids[] = $v['id'];
        }
        $this->view->assign("row", $row);
        $this->view->assign("groupids", $groupids);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        if ($ids) {
            $ids = array_intersect($this->childrenAdminIds, array_filter(explode(',', $ids)));
            // 避免越权删除管理员
            $childrenGroupIds = $this->childrenGroupIds;
            $adminList = $this->model->where('id', 'in', $ids)->where('id', 'in', function ($query) use ($childrenGroupIds) {
                $query->name('auth_group_access')->where('group_id', 'in', $childrenGroupIds)->field('uid');
            })->select();
            if ($adminList) {
                $deleteIds = [];
                foreach ($adminList as $k => $v) {
                    $deleteIds[] = $v->id;
                }
                $deleteIds = array_values(array_diff($deleteIds, [$this->auth->id]));
                if ($deleteIds) {
                    Db::startTrans();
                    try {
                        $this->model->destroy($deleteIds);
                        model('AuthGroupAccess')->where('uid', 'in', $deleteIds)->delete();
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }
                    $this->success();
                }
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('You have no permission'));
    }

    /**
     * 批量更新
     * @internal
     */
    public function multi($ids = "")
    {
        // 管理员禁止批量操作
        $this->error();
    }

    /**
     * 下拉搜索
     */
    public function selectpage()
    {
        $this->dataLimit = 'auth';
        $this->dataLimitField = 'id';
        return parent::selectpage();
    }

    /**
     * 构建时间查询条件
     */
    private function buildTimeWhere($timeRange, $startDate = '', $endDate = '')
    {
        $timeWhere = [];

        // 如果指定了自定义日期范围
        if ($timeRange === 'custom' && $startDate && $endDate) {
            $startTimestamp = strtotime($startDate . ' 00:00:00');
            $endTimestamp = strtotime($endDate . ' 23:59:59');

            // 验证时间戳是否有效
            if ($startTimestamp === false || $endTimestamp === false) {
                \think\Log::write('日期格式错误: ' . $startDate . ' - ' . $endDate, 'error');
                return [];
            }

            $timeWhere = ['create_time', 'between', [$startTimestamp, $endTimestamp]];
        } else {
            // 使用预设的时间范围
            switch ($timeRange) {
                case 'today':
                    $today = date('Y-m-d');
                    $startTimestamp = strtotime($today . ' 00:00:00');
                    $endTimestamp = strtotime($today . ' 23:59:59');
                    $timeWhere = ['create_time', 'between', [$startTimestamp, $endTimestamp]];
                    break;
                case 'yesterday':
                    $yesterday = date('Y-m-d', strtotime('-1 day'));
                    $startTimestamp = strtotime($yesterday . ' 00:00:00');
                    $endTimestamp = strtotime($yesterday . ' 23:59:59');
                    $timeWhere = ['create_time', 'between', [$startTimestamp, $endTimestamp]];
                    break;
                case 'week':
                    $startOfWeek = date('Y-m-d', strtotime('monday this week'));
                    $endOfWeek = date('Y-m-d', strtotime('sunday this week'));
                    $startTimestamp = strtotime($startOfWeek . ' 00:00:00');
                    $endTimestamp = strtotime($endOfWeek . ' 23:59:59');
                    $timeWhere = ['create_time', 'between', [$startTimestamp, $endTimestamp]];
                    break;
                case 'month':
                    $startOfMonth = date('Y-m-01');
                    $endOfMonth = date('Y-m-t');
                    $startTimestamp = strtotime($startOfMonth . ' 00:00:00');
                    $endTimestamp = strtotime($endOfMonth . ' 23:59:59');
                    $timeWhere = ['create_time', 'between', [$startTimestamp, $endTimestamp]];
                    break;
                case 'last_month':
                    $startOfLastMonth = date('Y-m-01', strtotime('first day of last month'));
                    $endOfLastMonth = date('Y-m-t', strtotime('last day of last month'));
                    $startTimestamp = strtotime($startOfLastMonth . ' 00:00:00');
                    $endTimestamp = strtotime($endOfLastMonth . ' 23:59:59');
                    $timeWhere = ['create_time', 'between', [$startTimestamp, $endTimestamp]];
                    break;
                case 'all':
                default:
                    // 不添加时间限制
                    $timeWhere = [];
                    break;
            }
        }

        // 记录时间查询条件用于调试
        if (!empty($timeWhere)) {
            \think\Log::write('时间查询条件: ' . json_encode($timeWhere) . ' (' . date('Y-m-d H:i:s', $timeWhere[2][0]) . ' - ' . date('Y-m-d H:i:s', $timeWhere[2][1]) . ')', 'info');
        }

        return $timeWhere;
    }
}


