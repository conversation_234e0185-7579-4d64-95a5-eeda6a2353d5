<?php
namespace Payment;

use think\Db;
use ChineseUsernameGenerator;
use fast\Http;
use think\Request;

class Alipay
{
    
    public static function QUICKWAPWAY($order)
    {
        $isplay = false;
        Db::startTrans();
        try {
            $order = \app\admin\model\Order::where('id', $order['id'])->lock(true)->find();
            if (!$order['pay_url']) {
                $account = db('account')->where('account_identity',$order['account_identity'])->find();
                $config = json_decode($account['config'],true);

                $djurl = $config['game_url'] . '/index.php';

                do {
                    $username = ChineseUsernameGenerator::generate_username();
                } while (mb_strlen($username, 'UTF-8') < 6);
                

                $res = Http::post($djurl."/user/reg.html?user_name={$username}&user_pwd=123123&user_pwd2=123123");
                trace("注册短剧账号：".$res,"error");
                if ($res == ''){
                    throw new \Exception("注册失败0");
                }
                
                $res = json_decode($res,true);
                if ($res['code']!==1 && $res['code']!==1005){
                    throw new \Exception("注册失败1");
                }

                $res = Http::get($djurl."/user/newbuy.html?price=".$order['amount']."&flag=pay&user=".$username);
                trace("生成短剧订单：".$res,"error");
                if ($res == ''){
                    throw new \Exception("下单失败0");
                }
                
                $res = json_decode($res,true);
                if ($res['code']!==1){
                    throw new \Exception("下单失败1");
                }

                $pay_url = $djurl."/user/newgopay.html?order_code=".$res['data']['order_code']."&payment=Tengpay&user=".$username;
                db('order')->where('id',$order['id'])->update(['pay_url'=>$pay_url,'pay_trade_no'=>$res['data']['order_code']]);
            } else {
                $isplay = true;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            trace($e->getMessage(), 'error');
            return ['code'=>0,'url'=>'','msg'=>'匹配失败'];
        }
        if ($isplay) {
            return ['code'=>1,'url'=>$order['pay_url'],'msg'=>'匹配成功'];
        } else {
            return ['code'=>1,'url'=>$pay_url,'msg'=>'匹配成功'];
        }
    }

    public static function QUICKWAPWAY2($order)
    {
        $isplay = false;
        Db::startTrans();
        try {
            $order = \app\admin\model\Order::where('id', $order['id'])->lock(true)->find();
            if (!$order['pay_url']) {
                $account = db('account')->where('account_identity',$order['account_identity'])->find();
                $config = json_decode($account['config'],true);

                $res = self::shoporder($config['game_url'],$order['out_trade_no'],$order['amount'],$order['create_time']);
                trace("生成商城订单：".$res,"error");

                // if ($res == ''){
                //     throw new \Exception("下单失败0");
                // }
                
                // $res = json_decode($res,true);
                // if ($res['code']!==0){
                //     throw new \Exception("下单失败1");
                // }

                $request = Request::instance();

                $alipay_config = [
                    'app_id' => $config['appid'],
                    'cert_mode' => 0,
                    'alipay_public_key' => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjpIdrJXm+LH6ZbMstR0gV3MInKrRYeGE0upMCuk0Qzg39SaN4N3ExCCbqGryirRcoNuO9Em/Cm/dvNmeOegb7Dc+Ni/itRuPtOEJwHYAPmZRbAa9dgpMIq+VmxU2HyWzsl+C0KfajCJKOzuHJEAEnuhihArRi1AXXD6Gv3PWHw6LL+odtUuu3GRcVhCfRK8eKTDksi1YGcy4fcv7CS55PhsVCk0GpPDoKjEM/iEftfV/nLnxkkkazfzY/JBOp3E85oycXwwdM4iSzEe2RcIyAmYa0P84pMGYaeurH/DOn6DGKWrHprLPZ6eo4eExBheF6E3ZI3NfShi1kaBn3tvIAQIDAQAB",
                    'app_private_key' => "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOkh2sleb4sfplsyy1HSBXcwicqtFh4YTS6kwK6TRDODf1Jo3g3cTEIJuoavKKtFyg2470Sb8Kb9282Z456BvsNz42L+K1G4+04QnAdgA+ZlFsBr12Ckwir5WbFTYfJbOyX4LQp9qMIko7O4ckQASe6GKECtGLUBdcPoa/c9YfDosv6h21S67cZFxWEJ9Erx4pMOSyLVgZzLh9y/sJLnk+GxUKTQak8OgqMQz+IR+19X+cufGSSRrN/Nj8kE6ncTzmjJxfDB0ziJLMR7ZFwjICZhrQ/zikwZhp66sf8M6foMYpasemss9np6jh4TEGF4XoTdkjc19KGLWRoGfe28gBAgMBAAECggEAFGABaabKqYQV+u7OVtvgwV6pPmqTHd/y8YmWcIC/fVPNe3WVFSvccQMP+9O88eGw0zRNi8/2Q3GVSOX43OG1C98hPvE8/xD/SqRWlnDGvCQ3Qq776KC0HMjnIpC3eWAT/Ev2EAfNDfXgkfKB78ZOYr9nROOe6r/5dq9g5n+RfEKJHkCH7WRQAaBn7Rb2Euv3e6gr+a48sJWXZ/JSpO6atY4gl8ru3gJ8f5HBtyx+JY0FM4HrgQvgzKCJwoDcUDn/omkMuEKbgtLDxgahZkU2Cgh0DOAp05d9fK536JRP+AEJxxZm15n9/yW3qdF8QfiLuZ0LrsGkBlo2r9540nr6AQKBgQD8D+/c4KvKWEc6fl92Ry8a3n+6OiDe+8FmgxlmFxEL/tIDew0CszAirmAO808Xpn2R1jvdvmYo8cnrVxNYD76RO+XLaPAmbJnyw+7rKS2T/vlzLuHSiXBbHSpt3yuyZEtfYaad2K2R8R99ER4GlJ54CcuYa88mostuBFLZUObvUQKBgQCQzEs1WlSiX0H9OvDeX+W8II3coPzUHQ1z1H5cHRpSEu23zO0Rv8Bf97yfHfKhgZQrpIOuLXIsZLezdBuiwpohIdvrqDqVRNHoxWzDGxZ+p+aArMTSMA3qlfQd9J1McDj7MdAjFK45HP9EC40nZZai/j+iaRrvINFE3v8lrfEBsQKBgQDXQEzX1M0fiO+a1hxzhyqEyYlN4SH9CgUIo9/mvMAD/GIgMs7ThW4Pz9WcLs9WJQmKpuaM4HGheYmzfbK5OwjEQeBlKoIabhexpvy6uXmArvcbE+gowNHAHVIEbdaFCv0KwPtmRIlE3iLDzQBVIkmJvOOukDJgjhknJG4TwvDTYQKBgGqdcfcL1utjllQdlQM3uxFlKu87vk10QtZ5rLyoVRv/gfdmtVvvQ3emEsVaKK/QmEf2L1sNbVbQpg5FPKAAB7rDkomW6ePEs4Zf73BNPqfteqReEeHtriF/Xiq8VSHWM66JhAaAkLPO7QmuM7XBcEP9Tt+H4mQWVuAODfIxfBZRAoGBAOB7Mr1IMEcGYj3GLx8NF6OPV2Tp1MdK1N4p+3Ce4CGbZqdNLHoK1R/WOWJJ+R3Hw8cOXdP96qxPYghQkVAZ6atAYOuadCGZDu+Bu7f8rSH5zgR9DQMFwmPOkFQXcHN8fKcGqVkQxq2h+l9jMz1kLocXgqDJZf6S7tytTI74p727",
                    'sign_type' => "RSA2",
                    'charset' => "UTF-8",
                    'gateway_url' => "https://openapi.alipay.com/gateway.do",
                    'pageMethod' => "1",
                    'return_url' => $config['game_url']."/h5/pay-success.html?orderNo={$order['out_trade_no']}&payAmount={$order['amount']}&payMethod=支付宝",
                    'notify_url' => $request->domain . '/api/order/notify',
                ];

                // 构造业务参数bizContent
                $bizContent = [
                    'out_trade_no' => $order['out_trade_no'], //商户订单号
                    'total_amount' => $order['amount'], //订单金额，单位为元
                    'subject' => '购物订单', //商品的标题
                ];
                // 发起支付请求
                try{
                    $aop = new \Alipay\AlipayTradeService($alipay_config);
                    $pay_url = $aop->wapPay($bizContent);
                }catch(Exception $e){
                    trace('支付宝下单失败:'.$e->getMessage(),'error');
                    throw new Exception("支付宝下单失败");
                }

                db('order')->where('id',$order['id'])->update(['pay_url'=>$pay_url]);
            } else {
                $isplay = true;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            trace($e->getMessage(), 'error');
            return ['code'=>0,'url'=>'','msg'=>'匹配失败'];
        }
        if ($isplay) {
            return ['code'=>1,'url'=>$order['pay_url'],'msg'=>'匹配成功'];
        } else {
            return ['code'=>1,'url'=>$pay_url,'msg'=>'匹配成功'];
        }
    }

    public static function shoporder($url,$out_trade_no,$price,$create_time)
    {
        // echo $out_trade_no;die;
        $curl = curl_init();
        curl_setopt_array($curl, [
        CURLOPT_URL => $url.'/replacebuy/shop/ordercreatenew/create.html',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => "out_trade_no={$out_trade_no}&price={$price}&create_time={$create_time}",
        CURLOPT_HTTPHEADER => [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept: application/json, text/javascript, */*; q=0.01',
            'Content-Type: application/x-www-form-urlencoded',
            'Accept-Language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
            'Cache-Control: no-cache',
            'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
            'Pragma: no-cache',
            'Sec-Fetch-Dest: empty',
            'Sec-Fetch-Mode: cors',
            'Sec-Fetch-Site: same-origin',
            'X-Requested-With: XMLHttpRequest',
            'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"',
        ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        return $response;
    }
}