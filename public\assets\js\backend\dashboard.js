define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template', 'toastr'], function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template, Toastr) {

    var Controller = {
        index: function () {
            // 通道数据从后端获取
            var channelData = [];
            var filteredData = [];
            var currentSort = { field: null, order: 'asc' };



            // 初始化页面
            loadChannelData();
            bindEvents();

            // 加载通道数据
            function loadChannelData() {
                $.ajax({
                    url: 'dashboard/getChannelData',
                    type: 'GET',
                    dataType: 'json',
                    data: { limit: 100 }, // 获取所有数据
                    success: function(response) {
                        if (response.code === 1) {
                            channelData = response.data.data || [];
                            filteredData = channelData.slice();
                            initChannelTable();
                        } else {
                            console.error('获取通道数据失败:', response.msg);
                            Toastr.error('获取通道数据失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('请求失败:', error);
                        Toastr.error('网络请求失败');
                    }
                });
            }

            // 初始化通道表格
            function initChannelTable() {
                renderChannelTable(filteredData);
            }

            // 渲染通道表格
            function renderChannelTable(data) {
                var tbody = $('#channelTableBody');
                tbody.empty();

                if (data.length === 0) {
                    tbody.append(
                        '<tr>' +
                            '<td colspan="7" class="text-center" style="padding: 40px;">' +
                                '<i class="fa fa-search" style="font-size: 48px; color: #ddd; margin-bottom: 10px;"></i>' +
                                '<p style="color: #999;">没有找到匹配的通道数据</p>' +
                            '</td>' +
                        '</tr>'
                    );
                    return;
                }

                $.each(data, function(index, channel) {
                    var ratioClass = channel.ratio >= 90 ? 'success' : channel.ratio >= 80 ? 'warning' : 'danger';

                    tbody.append(
                        '<tr data-channel="' + channel.code + '">' +
                            '<td>' +
                                '<strong>' + channel.name + '</strong>' +
                                '<br><small class="text-muted">' + channel.code + '</small>' +
                            '</td>' +
                            '<td>' +
                                '<span style="font-weight: 600; color: #27ae60;">¥' + channel.amount.toLocaleString() + '</span>' +
                            '</td>' +
                            '<td>' +
                                '<span class="label label-' + ratioClass + '">' + channel.ratio + '%</span>' +
                            '</td>' +
                            '<td>' +
                                '<span class="badge badge-success">' + channel.normal + '</span>' +
                            '</td>' +
                            '<td>' +
                                '<span class="badge badge-danger">' + channel.abnormal + '</span>' +
                            '</td>' +
                            '<td>' +
                                '<div class="btn-group btn-group-xs">' +
                                    '<button class="btn btn-primary btn-detail" data-channel="' + channel.code + '">' +
                                        '<i class="fa fa-eye"></i> 详情' +
                                    '</button>' +
                                    '<button class="btn btn-default btn-refresh" data-channel="' + channel.code + '">' +
                                        '<i class="fa fa-refresh"></i>' +
                                    '</button>' +
                                '</div>' +
                            '</td>' +
                        '</tr>'
                    );
                });
            }

            // 绑定事件
            function bindEvents() {
                // 搜索功能
                $('#channelSearch').on('input', function() {
                    filterData();
                });



                // 表格排序
                $('#channelTable th[data-sort]').on('click', function() {
                    var field = $(this).data('sort');
                    var currentOrder = currentSort.field === field && currentSort.order === 'asc' ? 'desc' : 'asc';

                    currentSort = { field: field, order: currentOrder };

                    // 更新排序图标
                    $('#channelTable th i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
                    $(this).find('i').removeClass('fa-sort').addClass(currentOrder === 'asc' ? 'fa-sort-up' : 'fa-sort-down');

                    sortData();
                    renderChannelTable(filteredData);
                });

                // 详情按钮
                $(document).on('click', '.btn-detail', function() {
                    var channelCode = $(this).data('channel');
                    showChannelDetail(channelCode);
                });

                // 刷新按钮
                $(document).on('click', '.btn-refresh', function() {
                    var channelCode = $(this).data('channel');
                    refreshChannelData(channelCode);
                });


            }

            // 数据筛选
            function filterData() {
                var searchTerm = $('#channelSearch').val().toLowerCase();

                filteredData = $.grep(channelData, function(channel) {
                    // 只保留搜索过滤
                    return channel.name.toLowerCase().indexOf(searchTerm) !== -1 ||
                           channel.code.toLowerCase().indexOf(searchTerm) !== -1;
                });

                sortData();
                renderChannelTable(filteredData);
            }

            // 数据排序
            function sortData() {
                if (!currentSort.field) return;

                filteredData.sort(function(a, b) {
                    var aVal = a[currentSort.field];
                    var bVal = b[currentSort.field];

                    // 处理不同数据类型
                    if (typeof aVal === 'string') {
                        aVal = aVal.toLowerCase();
                        bVal = bVal.toLowerCase();
                    }

                    if (currentSort.order === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });
            }

            // 刷新通道数据
            function refreshChannelData(channelCode) {
                var btn = $('.btn-refresh[data-channel="' + channelCode + '"]');
                var originalHtml = btn.html();

                btn.html('<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);

                // 调用后端刷新接口
                $.ajax({
                    url: 'dashboard/refreshChannel',
                    type: 'POST',
                    dataType: 'json',
                    data: { channel_code: channelCode },
                    success: function(response) {
                        btn.html(originalHtml).prop('disabled', false);

                        if (response.code === 1) {
                            Toastr.success('数据刷新成功');
                            // 重新加载通道数据
                            loadChannelData();
                        } else {
                            Toastr.error(response.msg || '刷新失败');
                        }
                    },
                    error: function() {
                        btn.html(originalHtml).prop('disabled', false);
                        Toastr.error('网络请求失败');
                    }
                });
            }



            // 显示通道详情
            function showChannelDetail(channelCode) {
                // 从后端获取通道详情
                $.ajax({
                    url: 'dashboard/getChannelDetail',
                    type: 'GET',
                    dataType: 'json',
                    data: { channel_code: channelCode },
                    success: function(response) {
                        if (response.code === 1) {
                            var channel = response.data;

                            var modalHtml =
                                '<div class="modal fade" id="channelDetailModal" tabindex="-1">' +
                                    '<div class="modal-dialog modal-lg">' +
                                        '<div class="modal-content">' +
                                            '<div class="modal-header">' +
                                                '<button type="button" class="close" data-dismiss="modal">&times;</button>' +
                                                '<h4 class="modal-title">' + channel.name + ' - 详细信息</h4>' +
                                            '</div>' +
                                            '<div class="modal-body">' +
                                                '<div class="row">' +
                                                    '<div class="col-md-6">' +
                                                        '<table class="table table-bordered">' +
                                                            '<tr><td>通道代码</td><td>' + channel.code + '</td></tr>' +
                                                            '<tr><td>通道名称</td><td>' + channel.name + '</td></tr>' +
                                                            '<tr><td>备注</td><td>' + (channel.notes || '无') + '</td></tr>' +
                                                            '<tr><td>今日收款</td><td>¥' + channel.amount.toLocaleString() + '</td></tr>' +
                                                            '<tr><td>成功率</td><td>' + channel.ratio + '%</td></tr>' +
                                                            '<tr><td>正常账号</td><td>' + channel.normal + '</td></tr>' +
                                                            '<tr><td>异常账号</td><td>' + channel.abnormal + '</td></tr>' +
                                                        '</table>' +
                                                    '</div>' +
                                                    '<div class="col-md-6">' +
                                                        '<h5>近7天收款统计</h5>' +
                                                        '<div class="table-responsive">' +
                                                            '<table class="table table-bordered table-striped" style="margin-bottom: 0;">' +
                                                                '<thead>' +
                                                                    '<tr>' +
                                                                        '<th>日期</th>' +
                                                                        '<th>收款金额</th>' +
                                                                    '</tr>' +
                                                                '</thead>' +
                                                                '<tbody id="detailTrendTable">' +
                                                                '</tbody>' +
                                                            '</table>' +
                                                        '</div>' +
                                                    '</div>' +
                                                '</div>' +
                                            '</div>' +
                                            '<div class="modal-footer">' +
                                                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                                            '</div>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>';

                            // 移除已存在的模态框
                            $('#channelDetailModal').remove();
                            $('body').append(modalHtml);
                            $('#channelDetailModal').modal('show');

                            // 渲染详情表格
                            setTimeout(function() {
                                renderDetailTable(channel);
                            }, 300);
                        } else {
                            Toastr.error(response.msg || '获取通道详情失败');
                        }
                    },
                    error: function() {
                        Toastr.error('网络请求失败');
                    }
                });
            }

            // 渲染详情表格
            function renderDetailTable(channel) {
                var tableBody = $('#detailTrendTable');
                if (!tableBody.length) {
                    console.warn('detailTrendTable element not found');
                    return;
                }

                tableBody.empty();

                // 生成近7天的日期
                var dates = [];
                for (var i = 6; i >= 0; i--) {
                    var date = new Date();
                    date.setDate(date.getDate() - i);
                    dates.push(date.toISOString().split('T')[0]);
                }

                // 渲染表格行
                for (var i = 0; i < dates.length; i++) {
                    var amount = channel.trend && channel.trend[i] ? channel.trend[i] : 0;
                    var formattedDate = dates[i];
                    var formattedAmount = '¥' + parseFloat(amount).toLocaleString();

                    tableBody.append(
                        '<tr>' +
                            '<td>' + formattedDate + '</td>' +
                            '<td style="font-weight: 600; color: #27ae60;">' + formattedAmount + '</td>' +
                        '</tr>'
                    );
                }
            }

            // 定时刷新数据
            setInterval(function() {
                // 这里应该调用实际的数据刷新接口
                console.log('定时刷新数据');
            }, 30000); // 30秒刷新一次

        }
    };

    return Controller;
});
