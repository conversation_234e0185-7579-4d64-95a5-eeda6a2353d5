define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order/alibscard/index' + location.search,
                    add_url: 'order/alibscard/add',
                    edit_url: 'order/alibscard/edit',
                    del_url: 'order/alibscard/del',
                    multi_url: 'order/alibscard/multi',
                    import_url: 'order/alibscard/import',
                    table: 'order',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'out_trade_no', title: __('商户订单号'), operate: 'LIKE'},
                        {field: 'local_trade_no', title: __('本站订单号'), operate: 'LIKE'},
                        {field: 'account_identity', title: __('账号标识'), operate: 'LIKE'},
                        {field: 'amount', title: __('充值金额'), operate:'BETWEEN'},
                        {field: 'channel_code', title: __('通道编码'), operate: 'LIKE'},
                        {field: 'pay_status', title: __('支付状态'), searchList: {"1":__('已完成'),"0":__('未支付')}, formatter: Table.api.formatter.status},
                        {field: 'callback_status', title: __('回调状态'), searchList: {"1":__('已回调'),"0":__('未回调')}, formatter: Table.api.formatter.status},
                        {field: 'callback_time', title: __('回调时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'create_time', title: __('创建时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'create_ip', title: __('客户端IP'), operate: 'LIKE'},
                        {field: 'merchants.name', title: __('商户'), operate: 'LIKE'},
                        {field: 'admin.nickname', title: __('归属'), operate:'BETWEEN'},
                        {field: 'notes', title: __('备注')},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            buttons :[
                                {
                                    name: 'callback',
                                    text: '',
                                    title: '回调',
                                    icon: 'fa fa-retweet',
                                    classname: 'btn btn-xs btn-info btn-view btn-ajax',
                                    url: 'order/alibscard/callback',
                                    refresh: true,
                                    confirm: '确认是否要回调',
                                    success: function (data, ret) {
                                        if(ret.code===1){
                                            Layer.alert(ret.msg);
                                        }
                                        else {
                                            Layer.alert('<span id="" style="color: red;">'+ret.msg+'</span>');
                                        }
                                    },
                                    error: function (data, ret) {
                                        console.log(data, ret);
                                        Layer.alert(ret.msg);
                                        return false;
                                    }
                                }
                            ],
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
