<?php

namespace app\cashier\controller;
use think\Controller;

class Index extends Controller
{
    public function index()
    {
        $param = input();
        $order = db('order')->where('local_trade_no',$param['order'])->find();
        if (!$order) {
            $this->error('订单不存在');
        }
        
        // $request = Request::instance();
        // $header = $request->header();
        // db('order')->where('id',$order['id'])->update(['create_ip'=>$header['x-forwarded-for']]);

        // // 当前IP今日已经支付的订单数量
        // $todayIspayCount = db('order')
        //     ->where('pay_status',1)
        //     ->where('create_ip',$header['x-forwarded-for'])
        //     ->whereTime('create_time', 'today')
        //     ->count('id');
        // $todayCount = db('order')
        //     ->where('create_ip',$header['x-forwarded-for'])
        //     ->whereTime('create_time', 'today')
        //     ->count('id');
        // if ($todayIspayCount>3 || $todayCount>5){
        //     db('order')->where('id',$order['id'])->update(['notes'=>"IP限制"]);
        //     $this->error('IP限制');
        // }
        $this->assign('order',$order);
        return view();
    }
    
    public function getOrderInfo()
    {
        $param = input();
        $order = db('order')->where('local_trade_no',$param['trade_no'])->find();
        if (!$order) return json(['code'=>0,'url'=>'','msg'=>'订单不存在']);
        switch($param['channel']){
            case '9003':
                $data = \Payment\Alipay::QUICKWAPWAY($order);
                break;
            case '9004':
                $data = \Payment\Alipay::QUICKWAPWAY2($order);
                break;
            default:
                $data = ['code'=>0,'url'=>'','msg'=>'不支持的通道'];
                break;
        }
        return json($data);
    }

    // 预授权后发起扣款
    public function preauth_pay()
    {
        $param = input();
        trace($param['data'],'error');
        $data = json_decode($param['data'],true);
        // 授权成功后，调用转支付
        if($data['status'] == 'SUCCESS'){
            $order = db('order')->where('out_trade_no',$data['out_order_no'])->find();
            if(!$order) return json(['code'=>0,'msg'=>'订单不存在']);
            if($order['pay_status']==1) return json(['code'=>1,'msg'=>'订单已支付']);
            if($order['auth_no']) return json(['code'=>1,'msg'=>'订单已授权']);
            $account = db('account')->where('account_identity',$order['account_identity'])->find();
            $account_config = json_decode($account['config'],true);
            $account_config['private_key'] = config('site.alipay_private_key');
            $account_config['public_key'] = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjpIdrJXm+LH6ZbMstR0gV3MInKrRYeGE0upMCuk0Qzg39SaN4N3ExCCbqGryirRcoNuO9Em/Cm/dvNmeOegb7Dc+Ni/itRuPtOEJwHYAPmZRbAa9dgpMIq+VmxU2HyWzsl+C0KfajCJKOzuHJEAEnuhihArRi1AXXD6Gv3PWHw6LL+odtUuu3GRcVhCfRK8eKTDksi1YGcy4fcv7CS55PhsVCk0GpPDoKjEM/iEftfV/nLnxkkkazfzY/JBOp3E85oycXwwdM4iSzEe2RcIyAmYa0P84pMGYaeurH/DOn6DGKWrHprLPZ6eo4eExBheF6E3ZI3NfShi1kaBn3tvIAQIDAQAB";
            $res = \Payment\Service::froZenTopay($account_config['appid'],$account_config['public_key'],$account_config['private_key'],$order['out_trade_no'],$order['amount'],$data['auth_no']);
            if($res['success']){
                db('order')->where('id',$order['id'])->update(['auth_no'=>$data['auth_no'],'pay_trade_no'=>$res['trade_no']]);
                return json(['code'=>1,'msg'=>'发起支付成功']);
            }
            return json(['code'=>0,'msg'=>$res['msg']]);
        }
        return json(['code'=>0,'msg'=>'预授权失败,请重试']);
    }

    // 预授权支付页面
    public function preauth_page()
    {
        $param = input();
        $order = db('order')->where('out_trade_no',$param['trade_no'])->find();
        if (!$order) $this->error('订单不存在');
        if ($order['pay_status']==1) $this->error('订单已支付');
        if ($order['auth_no']) $this->error('订单已授权');
        if(empty($order['pay_url'])){
            // 获取账号配置
            $account = db('account')->where('account_identity',$order['account_identity'])->find();
            $account_config = json_decode($account['config'],true);
            $account_config['private_key'] = config('site.alipay_private_key');
            $account_config['public_key'] = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjpIdrJXm+LH6ZbMstR0gV3MInKrRYeGE0upMCuk0Qzg39SaN4N3ExCCbqGryirRcoNuO9Em/Cm/dvNmeOegb7Dc+Ni/itRuPtOEJwHYAPmZRbAa9dgpMIq+VmxU2HyWzsl+C0KfajCJKOzuHJEAEnuhihArRi1AXXD6Gv3PWHw6LL+odtUuu3GRcVhCfRK8eKTDksi1YGcy4fcv7CS55PhsVCk0GpPDoKjEM/iEftfV/nLnxkkkazfzY/JBOp3E85oycXwwdM4iSzEe2RcIyAmYa0P84pMGYaeurH/DOn6DGKWrHprLPZ6eo4eExBheF6E3ZI3NfShi1kaBn3tvIAQIDAQAB";
            // 发起预授权
            $orderStr = \Payment\Service::getPreAuthFreezeOrderInfo($account_config['appid'],$account_config['public_key'],$account_config['private_key'],$order['out_trade_no'],$order['amount']);
            if($orderStr){
                db('order')->where('id',$order['id'])->update(['pay_url'=>$orderStr]);
                $order['pay_url'] = $orderStr;
            }else{
                $this->error('获取授权信息失败');
            }
        }
        $this->assign('order',$order);
        return view();
    }

    // 预授权支付api,给小程序用的
    public function preauth_api()
    {
        $param = input();
        $order = db('order')->where('out_trade_no',$param['trade_no'])->find();
        if (!$order) return json(['code'=>0,'msg'=>'订单不存在']);
        if ($order['pay_status']==1) return json(['code'=>0,'msg'=>'订单已支付']);
        if ($order['auth_no']) return json(['code'=>0,'msg'=>'订单已授权']);
        if(empty($order['pay_url'])){
            // 获取账号配置
            $account = db('account')->where('account_identity',$order['account_identity'])->find();
            $account_config = json_decode($account['config'],true);
            $account_config['private_key'] = config('site.alipay_private_key');
            $account_config['public_key'] = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjpIdrJXm+LH6ZbMstR0gV3MInKrRYeGE0upMCuk0Qzg39SaN4N3ExCCbqGryirRcoNuO9Em/Cm/dvNmeOegb7Dc+Ni/itRuPtOEJwHYAPmZRbAa9dgpMIq+VmxU2HyWzsl+C0KfajCJKOzuHJEAEnuhihArRi1AXXD6Gv3PWHw6LL+odtUuu3GRcVhCfRK8eKTDksi1YGcy4fcv7CS55PhsVCk0GpPDoKjEM/iEftfV/nLnxkkkazfzY/JBOp3E85oycXwwdM4iSzEe2RcIyAmYa0P84pMGYaeurH/DOn6DGKWrHprLPZ6eo4eExBheF6E3ZI3NfShi1kaBn3tvIAQIDAQAB";
            // 发起预授权
            $orderStr = \Payment\Service::getPreAuthFreezeOrderInfo($account_config['appid'],$account_config['public_key'],$account_config['private_key'],$order['out_trade_no'],$order['amount']);
            if($orderStr){
                db('order')->where('id',$order['id'])->update(['pay_url'=>$orderStr]);
                $order['pay_url'] = $orderStr;
            }else{
                return json(['code'=>0,'msg'=>'获取授权信息失败']);
            }
        }
        return json(['code'=>1,'msg'=>'发起预授权成功','data'=>$order]);
    }
    
    // 解冻
    public function unfreeze()
    {
        $order = db('order')->where('out_trade_no','W609604501742678')->find();
        // 获取账号配置
        $account = db('account')->where('account_identity',$order['account_identity'])->find();
        $account_config = json_decode($account['config'],true);
        $account_config['private_key'] = config('site.alipay_private_key');
        $account_config['public_key'] = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjpIdrJXm+LH6ZbMstR0gV3MInKrRYeGE0upMCuk0Qzg39SaN4N3ExCCbqGryirRcoNuO9Em/Cm/dvNmeOegb7Dc+Ni/itRuPtOEJwHYAPmZRbAa9dgpMIq+VmxU2HyWzsl+C0KfajCJKOzuHJEAEnuhihArRi1AXXD6Gv3PWHw6LL+odtUuu3GRcVhCfRK8eKTDksi1YGcy4fcv7CS55PhsVCk0GpPDoKjEM/iEftfV/nLnxkkkazfzY/JBOp3E85oycXwwdM4iSzEe2RcIyAmYa0P84pMGYaeurH/DOn6DGKWrHprLPZ6eo4eExBheF6E3ZI3NfShi1kaBn3tvIAQIDAQAB";
        $res = \Payment\Service::unfreeze($account_config['appid'],$account_config['public_key'],$account_config['private_key'],$order['local_trade_no'],$order['amount'],$order['auth_no']);
        return json($res);
    }
}